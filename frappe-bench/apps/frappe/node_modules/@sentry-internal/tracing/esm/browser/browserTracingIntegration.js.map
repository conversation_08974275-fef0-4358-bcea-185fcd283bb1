{"version": 3, "file": "browserTracingIntegration.js", "sources": ["../../../src/browser/browserTracingIntegration.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { IdleTransaction } from '@sentry/core';\nimport { getActiveSpan, getClient, getCurrentScope } from '@sentry/core';\nimport { getCurrentHub } from '@sentry/core';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  TRACING_DEFAULTS,\n  addTracingExtensions,\n  getActiveTransaction,\n  spanToJSON,\n  startIdleTransaction,\n} from '@sentry/core';\nimport type {\n  Client,\n  Integration,\n  IntegrationFn,\n  StartSpanOptions,\n  Transaction,\n  TransactionContext,\n  TransactionSource,\n} from '@sentry/types';\nimport type { Span } from '@sentry/types';\nimport {\n  addHistoryInstrumentationHandler,\n  browserPerformanceTimeOrigin,\n  getDomElement,\n  logger,\n  propagationContextFromHeaders,\n} from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../common/debug-build';\nimport { registerBackgroundTabDetection } from './backgroundtab';\nimport { addPerformanceInstrumentationHandler } from './instrument';\nimport {\n  addPerformanceEntries,\n  startTrackingINP,\n  startTrackingInteractions,\n  startTrackingLongTasks,\n  startTrackingWebVitals,\n} from './metrics';\nimport type { RequestInstrumentationOptions } from './request';\nimport { defaultRequestInstrumentationOptions, instrumentOutgoingRequests } from './request';\nimport { WINDOW } from './types';\nimport type { InteractionRouteNameMapping } from './web-vitals/types';\n\nexport const BROWSER_TRACING_INTEGRATION_ID = 'BrowserTracing';\n\n/** Options for Browser Tracing integration */\nexport interface BrowserTracingOptions extends RequestInstrumentationOptions {\n  /**\n   * The time to wait in ms until the transaction will be finished during an idle state. An idle state is defined\n   * by a moment where there are no in-progress spans.\n   *\n   * The transaction will use the end timestamp of the last finished span as the endtime for the transaction.\n   * If there are still active spans when this the `idleTimeout` is set, the `idleTimeout` will get reset.\n   * Time is in ms.\n   *\n   * Default: 1000\n   */\n  idleTimeout: number;\n\n  /**\n   * The max duration for a transaction. If a transaction duration hits the `finalTimeout` value, it\n   * will be finished.\n   * Time is in ms.\n   *\n   * Default: 30000\n   */\n  finalTimeout: number;\n\n  /**\n   * The heartbeat interval. If no new spans are started or open spans are finished within 3 heartbeats,\n   * the transaction will be finished.\n   * Time is in ms.\n   *\n   * Default: 5000\n   */\n  heartbeatInterval: number;\n\n  /**\n   * If a span should be created on page load.\n   * If this is set to `false`, this integration will not start the default page load span.\n   * Default: true\n   */\n  instrumentPageLoad: boolean;\n\n  /**\n   * If a span should be created on navigation (history change).\n   * If this is set to `false`, this integration will not start the default navigation spans.\n   * Default: true\n   */\n  instrumentNavigation: boolean;\n\n  /**\n   * Flag spans where tabs moved to background with \"cancelled\". Browser background tab timing is\n   * not suited towards doing precise measurements of operations. By default, we recommend that this option\n   * be enabled as background transactions can mess up your statistics in nondeterministic ways.\n   *\n   * Default: true\n   */\n  markBackgroundSpan: boolean;\n\n  /**\n   * If true, Sentry will capture long tasks and add them to the corresponding transaction.\n   *\n   * Default: true\n   */\n  enableLongTask: boolean;\n\n  /**\n   * If true, Sentry will capture INP web vitals as standalone spans .\n   *\n   * Default: false\n   */\n  enableInp: boolean;\n\n  /**\n   * Sample rate to determine interaction span sampling.\n   * interactionsSampleRate is applied on top of the global tracesSampleRate.\n   * ie a tracesSampleRate of 0.1 and interactionsSampleRate of 0.5 will result in a 0.05 sample rate for interactions.\n   *\n   * Default: 1\n   */\n  interactionsSampleRate: number;\n\n  /**\n   * _metricOptions allows the user to send options to change how metrics are collected.\n   *\n   * _metricOptions is currently experimental.\n   *\n   * Default: undefined\n   */\n  _metricOptions?: Partial<{\n    /**\n     * @deprecated This property no longer has any effect and will be removed in v8.\n     */\n    _reportAllChanges: boolean;\n  }>;\n\n  /**\n   * _experiments allows the user to send options to define how this integration works.\n   * Note that the `enableLongTask` options is deprecated in favor of the option at the top level, and will be removed in v8.\n   *\n   * TODO (v8): Remove enableLongTask\n   *\n   * Default: undefined\n   */\n  _experiments: Partial<{\n    enableInteractions: boolean;\n  }>;\n\n  /**\n   * A callback which is called before a span for a pageload or navigation is started.\n   * It receives the options passed to `startSpan`, and expects to return an updated options object.\n   */\n  beforeStartSpan?: (options: StartSpanOptions) => StartSpanOptions;\n}\n\nconst DEFAULT_BROWSER_TRACING_OPTIONS: BrowserTracingOptions = {\n  ...TRACING_DEFAULTS,\n  instrumentNavigation: true,\n  instrumentPageLoad: true,\n  markBackgroundSpan: true,\n  enableLongTask: true,\n  enableInp: false,\n  interactionsSampleRate: 1,\n  _experiments: {},\n  ...defaultRequestInstrumentationOptions,\n};\n\n/**\n * The Browser Tracing integration automatically instruments browser pageload/navigation\n * actions as transactions, and captures requests, metrics and errors as spans.\n *\n * The integration can be configured with a variety of options, and can be extended to use\n * any routing library. This integration uses {@see IdleTransaction} to create transactions.\n *\n * We explicitly export the proper type here, as this has to be extended in some cases.\n */\nexport const browserTracingIntegration = ((_options: Partial<BrowserTracingOptions> = {}) => {\n  const _hasSetTracePropagationTargets = DEBUG_BUILD\n    ? !!(\n        // eslint-disable-next-line deprecation/deprecation\n        (_options.tracePropagationTargets || _options.tracingOrigins)\n      )\n    : false;\n\n  addTracingExtensions();\n\n  // TODO (v8): remove this block after tracingOrigins is removed\n  // Set tracePropagationTargets to tracingOrigins if specified by the user\n  // In case both are specified, tracePropagationTargets takes precedence\n  // eslint-disable-next-line deprecation/deprecation\n  if (!_options.tracePropagationTargets && _options.tracingOrigins) {\n    // eslint-disable-next-line deprecation/deprecation\n    _options.tracePropagationTargets = _options.tracingOrigins;\n  }\n\n  const options = {\n    ...DEFAULT_BROWSER_TRACING_OPTIONS,\n    ..._options,\n  };\n\n  const _collectWebVitals = startTrackingWebVitals();\n\n  /** Stores a mapping of interactionIds from PerformanceEventTimings to the origin interaction path */\n  const interactionIdToRouteNameMapping: InteractionRouteNameMapping = {};\n  if (options.enableInp) {\n    startTrackingINP(interactionIdToRouteNameMapping, options.interactionsSampleRate);\n  }\n\n  if (options.enableLongTask) {\n    startTrackingLongTasks();\n  }\n  if (options._experiments.enableInteractions) {\n    startTrackingInteractions();\n  }\n\n  const latestRoute: {\n    name: string | undefined;\n    context: TransactionContext | undefined;\n  } = {\n    name: undefined,\n    context: undefined,\n  };\n\n  /** Create routing idle transaction. */\n  function _createRouteTransaction(context: TransactionContext): Transaction | undefined {\n    // eslint-disable-next-line deprecation/deprecation\n    const hub = getCurrentHub();\n\n    const { beforeStartSpan, idleTimeout, finalTimeout, heartbeatInterval } = options;\n\n    const isPageloadTransaction = context.op === 'pageload';\n\n    let expandedContext: TransactionContext;\n    if (isPageloadTransaction) {\n      const sentryTrace = isPageloadTransaction ? getMetaContent('sentry-trace') : '';\n      const baggage = isPageloadTransaction ? getMetaContent('baggage') : undefined;\n      const { traceId, dsc, parentSpanId, sampled } = propagationContextFromHeaders(sentryTrace, baggage);\n      expandedContext = {\n        traceId,\n        parentSpanId,\n        parentSampled: sampled,\n        ...context,\n        metadata: {\n          // eslint-disable-next-line deprecation/deprecation\n          ...context.metadata,\n          dynamicSamplingContext: dsc,\n        },\n        trimEnd: true,\n      };\n    } else {\n      expandedContext = {\n        trimEnd: true,\n        ...context,\n      };\n    }\n\n    const finalContext = beforeStartSpan ? beforeStartSpan(expandedContext) : expandedContext;\n\n    // If `beforeStartSpan` set a custom name, record that fact\n    // eslint-disable-next-line deprecation/deprecation\n    finalContext.metadata =\n      finalContext.name !== expandedContext.name\n        ? // eslint-disable-next-line deprecation/deprecation\n          { ...finalContext.metadata, source: 'custom' }\n        : // eslint-disable-next-line deprecation/deprecation\n          finalContext.metadata;\n\n    latestRoute.name = finalContext.name;\n    latestRoute.context = finalContext;\n\n    if (finalContext.sampled === false) {\n      DEBUG_BUILD && logger.log(`[Tracing] Will not send ${finalContext.op} transaction because of beforeNavigate.`);\n    }\n\n    DEBUG_BUILD && logger.log(`[Tracing] Starting ${finalContext.op} transaction on scope`);\n\n    const { location } = WINDOW;\n\n    const idleTransaction = startIdleTransaction(\n      hub,\n      finalContext,\n      idleTimeout,\n      finalTimeout,\n      true,\n      { location }, // for use in the tracesSampler\n      heartbeatInterval,\n      isPageloadTransaction, // should wait for finish signal if it's a pageload transaction\n    );\n\n    if (isPageloadTransaction && WINDOW.document) {\n      WINDOW.document.addEventListener('readystatechange', () => {\n        if (['interactive', 'complete'].includes(WINDOW.document!.readyState)) {\n          idleTransaction.sendAutoFinishSignal();\n        }\n      });\n\n      if (['interactive', 'complete'].includes(WINDOW.document.readyState)) {\n        idleTransaction.sendAutoFinishSignal();\n      }\n    }\n\n    idleTransaction.registerBeforeFinishCallback(transaction => {\n      _collectWebVitals();\n      addPerformanceEntries(transaction);\n    });\n\n    return idleTransaction as Transaction;\n  }\n\n  return {\n    name: BROWSER_TRACING_INTEGRATION_ID,\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    setupOnce: () => {},\n    afterAllSetup(client) {\n      const clientOptions = client.getOptions();\n\n      const { markBackgroundSpan, traceFetch, traceXHR, shouldCreateSpanForRequest, enableHTTPTimings, _experiments } =\n        options;\n\n      const clientOptionsTracePropagationTargets = clientOptions && clientOptions.tracePropagationTargets;\n      // There are three ways to configure tracePropagationTargets:\n      // 1. via top level client option `tracePropagationTargets`\n      // 2. via BrowserTracing option `tracePropagationTargets`\n      // 3. via BrowserTracing option `tracingOrigins` (deprecated)\n      //\n      // To avoid confusion, favour top level client option `tracePropagationTargets`, and fallback to\n      // BrowserTracing option `tracePropagationTargets` and then `tracingOrigins` (deprecated).\n      // This is done as it minimizes bundle size (we don't have to have undefined checks).\n      //\n      // If both 1 and either one of 2 or 3 are set (from above), we log out a warning.\n      // eslint-disable-next-line deprecation/deprecation\n      const tracePropagationTargets = clientOptionsTracePropagationTargets || options.tracePropagationTargets;\n      if (DEBUG_BUILD && _hasSetTracePropagationTargets && clientOptionsTracePropagationTargets) {\n        logger.warn(\n          '[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.',\n        );\n      }\n\n      let activeSpan: Span | undefined;\n      let startingUrl: string | undefined = WINDOW.location && WINDOW.location.href;\n\n      if (client.on) {\n        client.on('startNavigationSpan', (context: StartSpanOptions) => {\n          if (activeSpan) {\n            DEBUG_BUILD && logger.log(`[Tracing] Finishing current transaction with op: ${spanToJSON(activeSpan).op}`);\n            // If there's an open transaction on the scope, we need to finish it before creating an new one.\n            activeSpan.end();\n          }\n          activeSpan = _createRouteTransaction({\n            op: 'navigation',\n            ...context,\n          });\n        });\n\n        client.on('startPageLoadSpan', (context: StartSpanOptions) => {\n          if (activeSpan) {\n            DEBUG_BUILD && logger.log(`[Tracing] Finishing current transaction with op: ${spanToJSON(activeSpan).op}`);\n            // If there's an open transaction on the scope, we need to finish it before creating an new one.\n            activeSpan.end();\n          }\n          activeSpan = _createRouteTransaction({\n            op: 'pageload',\n            ...context,\n          });\n        });\n      }\n\n      if (options.instrumentPageLoad && client.emit && WINDOW.location) {\n        const context: StartSpanOptions = {\n          name: WINDOW.location.pathname,\n          // pageload should always start at timeOrigin (and needs to be in s, not ms)\n          startTimestamp: browserPerformanceTimeOrigin ? browserPerformanceTimeOrigin / 1000 : undefined,\n          origin: 'auto.pageload.browser',\n          attributes: {\n            [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n          },\n        };\n        startBrowserTracingPageLoadSpan(client, context);\n      }\n\n      if (options.instrumentNavigation && client.emit && WINDOW.location) {\n        addHistoryInstrumentationHandler(({ to, from }) => {\n          /**\n           * This early return is there to account for some cases where a navigation transaction starts right after\n           * long-running pageload. We make sure that if `from` is undefined and a valid `startingURL` exists, we don't\n           * create an uneccessary navigation transaction.\n           *\n           * This was hard to duplicate, but this behavior stopped as soon as this fix was applied. This issue might also\n           * only be caused in certain development environments where the usage of a hot module reloader is causing\n           * errors.\n           */\n          if (from === undefined && startingUrl && startingUrl.indexOf(to) !== -1) {\n            startingUrl = undefined;\n            return;\n          }\n\n          if (from !== to) {\n            startingUrl = undefined;\n            const context: StartSpanOptions = {\n              name: WINDOW.location.pathname,\n              origin: 'auto.navigation.browser',\n              attributes: {\n                [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'url',\n              },\n            };\n\n            startBrowserTracingNavigationSpan(client, context);\n          }\n        });\n      }\n\n      if (markBackgroundSpan) {\n        registerBackgroundTabDetection();\n      }\n\n      if (_experiments.enableInteractions) {\n        registerInteractionListener(options, latestRoute);\n      }\n\n      if (options.enableInp) {\n        registerInpInteractionListener(interactionIdToRouteNameMapping, latestRoute);\n      }\n\n      instrumentOutgoingRequests({\n        traceFetch,\n        traceXHR,\n        tracePropagationTargets,\n        shouldCreateSpanForRequest,\n        enableHTTPTimings,\n      });\n    },\n    // TODO v8: Remove this again\n    // This is private API that we use to fix converted BrowserTracing integrations in Next.js & SvelteKit\n    options,\n  };\n}) satisfies IntegrationFn;\n\n/**\n * Manually start a page load span.\n * This will only do something if the BrowserTracing integration has been setup.\n */\nexport function startBrowserTracingPageLoadSpan(client: Client, spanOptions: StartSpanOptions): Span | undefined {\n  if (!client.emit) {\n    return;\n  }\n\n  client.emit('startPageLoadSpan', spanOptions);\n\n  const span = getActiveSpan();\n  const op = span && spanToJSON(span).op;\n  return op === 'pageload' ? span : undefined;\n}\n\n/**\n * Manually start a navigation span.\n * This will only do something if the BrowserTracing integration has been setup.\n */\nexport function startBrowserTracingNavigationSpan(client: Client, spanOptions: StartSpanOptions): Span | undefined {\n  if (!client.emit) {\n    return;\n  }\n\n  client.emit('startNavigationSpan', spanOptions);\n\n  const span = getActiveSpan();\n  const op = span && spanToJSON(span).op;\n  return op === 'navigation' ? span : undefined;\n}\n\n/** Returns the value of a meta tag */\nexport function getMetaContent(metaName: string): string | undefined {\n  // Can't specify generic to `getDomElement` because tracing can be used\n  // in a variety of environments, have to disable `no-unsafe-member-access`\n  // as a result.\n  const metaTag = getDomElement(`meta[name=${metaName}]`);\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return metaTag ? metaTag.getAttribute('content') : undefined;\n}\n\n/** Start listener for interaction transactions */\nfunction registerInteractionListener(\n  options: BrowserTracingOptions,\n  latestRoute: {\n    name: string | undefined;\n    context: TransactionContext | undefined;\n  },\n): void {\n  let inflightInteractionTransaction: IdleTransaction | undefined;\n  const registerInteractionTransaction = (): void => {\n    const { idleTimeout, finalTimeout, heartbeatInterval } = options;\n    const op = 'ui.action.click';\n\n    // eslint-disable-next-line deprecation/deprecation\n    const currentTransaction = getActiveTransaction();\n    if (currentTransaction && currentTransaction.op && ['navigation', 'pageload'].includes(currentTransaction.op)) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `[Tracing] Did not create ${op} transaction because a pageload or navigation transaction is in progress.`,\n        );\n      return undefined;\n    }\n\n    if (inflightInteractionTransaction) {\n      inflightInteractionTransaction.setFinishReason('interactionInterrupted');\n      inflightInteractionTransaction.end();\n      inflightInteractionTransaction = undefined;\n    }\n\n    if (!latestRoute.name) {\n      DEBUG_BUILD && logger.warn(`[Tracing] Did not create ${op} transaction because _latestRouteName is missing.`);\n      return undefined;\n    }\n\n    const { location } = WINDOW;\n\n    const context: TransactionContext = {\n      name: latestRoute.name,\n      op,\n      trimEnd: true,\n      data: {\n        [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: latestRoute.context ? getSource(latestRoute.context) : undefined || 'url',\n      },\n    };\n\n    inflightInteractionTransaction = startIdleTransaction(\n      // eslint-disable-next-line deprecation/deprecation\n      getCurrentHub(),\n      context,\n      idleTimeout,\n      finalTimeout,\n      true,\n      { location }, // for use in the tracesSampler\n      heartbeatInterval,\n    );\n  };\n\n  ['click'].forEach(type => {\n    if (WINDOW.document) {\n      addEventListener(type, registerInteractionTransaction, { once: false, capture: true });\n    }\n  });\n}\n\nfunction isPerformanceEventTiming(entry: PerformanceEntry): entry is PerformanceEventTiming {\n  return 'duration' in entry;\n}\n\n/** We store up to 10 interaction candidates max to cap memory usage. This is the same cap as getINP from web-vitals */\nconst MAX_INTERACTIONS = 10;\n\n/** Creates a listener on interaction entries, and maps interactionIds to the origin path of the interaction */\nfunction registerInpInteractionListener(\n  interactionIdToRouteNameMapping: InteractionRouteNameMapping,\n  latestRoute: {\n    name: string | undefined;\n    context: TransactionContext | undefined;\n  },\n): void {\n  const handleEntries = ({ entries }: { entries: PerformanceEntry[] }): void => {\n    const client = getClient();\n    // We need to get the replay, user, and activeTransaction from the current scope\n    // so that we can associate replay id, profile id, and a user display to the span\n    const replay =\n      client !== undefined && client.getIntegrationByName !== undefined\n        ? (client.getIntegrationByName('Replay') as Integration & { getReplayId: () => string })\n        : undefined;\n    const replayId = replay !== undefined ? replay.getReplayId() : undefined;\n    // eslint-disable-next-line deprecation/deprecation\n    const activeTransaction = getActiveTransaction();\n    const currentScope = getCurrentScope();\n    const user = currentScope !== undefined ? currentScope.getUser() : undefined;\n    entries.forEach(entry => {\n      if (isPerformanceEventTiming(entry)) {\n        const interactionId = entry.interactionId;\n        if (interactionId === undefined) {\n          return;\n        }\n        const existingInteraction = interactionIdToRouteNameMapping[interactionId];\n        const duration = entry.duration;\n        const startTime = entry.startTime;\n        const keys = Object.keys(interactionIdToRouteNameMapping);\n        const minInteractionId =\n          keys.length > 0\n            ? keys.reduce((a, b) => {\n                return interactionIdToRouteNameMapping[a].duration < interactionIdToRouteNameMapping[b].duration\n                  ? a\n                  : b;\n              })\n            : undefined;\n        // For a first input event to be considered, we must check that an interaction event does not already exist with the same duration and start time.\n        // This is also checked in the web-vitals library.\n        if (entry.entryType === 'first-input') {\n          const matchingEntry = keys\n            .map(key => interactionIdToRouteNameMapping[key])\n            .some(interaction => {\n              return interaction.duration === duration && interaction.startTime === startTime;\n            });\n          if (matchingEntry) {\n            return;\n          }\n        }\n        // Interactions with an id of 0 and are not first-input are not valid.\n        if (!interactionId) {\n          return;\n        }\n        // If the interaction already exists, we want to use the duration of the longest entry, since that is what the INP metric uses.\n        if (existingInteraction) {\n          existingInteraction.duration = Math.max(existingInteraction.duration, duration);\n        } else if (\n          keys.length < MAX_INTERACTIONS ||\n          minInteractionId === undefined ||\n          duration > interactionIdToRouteNameMapping[minInteractionId].duration\n        ) {\n          // If the interaction does not exist, we want to add it to the mapping if there is space, or if the duration is longer than the shortest entry.\n          const routeName = latestRoute.name;\n          const parentContext = latestRoute.context;\n          if (routeName && parentContext) {\n            if (minInteractionId && Object.keys(interactionIdToRouteNameMapping).length >= MAX_INTERACTIONS) {\n              // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n              delete interactionIdToRouteNameMapping[minInteractionId];\n            }\n            interactionIdToRouteNameMapping[interactionId] = {\n              routeName,\n              duration,\n              parentContext,\n              user,\n              activeTransaction,\n              replayId,\n              startTime,\n            };\n          }\n        }\n      }\n    });\n  };\n  addPerformanceInstrumentationHandler('event', handleEntries);\n  addPerformanceInstrumentationHandler('first-input', handleEntries);\n}\n\nfunction getSource(context: TransactionContext): TransactionSource | undefined {\n  const sourceFromAttributes = context.attributes && context.attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n  // eslint-disable-next-line deprecation/deprecation\n  const sourceFromData = context.data && context.data[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n  // eslint-disable-next-line deprecation/deprecation\n  const sourceFromMetadata = context.metadata && context.metadata.source;\n\n  return sourceFromAttributes || sourceFromData || sourceFromMetadata;\n}\n"], "names": [], "mappings": ";;;;;;;;;AA6CO,MAAM,8BAA+B,GAAE,iBAAgB;AAC9D;AACA;;AA+GA,MAAM,+BAA+B,GAA0B;AAC/D,EAAE,GAAG,gBAAgB;AACrB,EAAE,oBAAoB,EAAE,IAAI;AAC5B,EAAE,kBAAkB,EAAE,IAAI;AAC1B,EAAE,kBAAkB,EAAE,IAAI;AAC1B,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,SAAS,EAAE,KAAK;AAClB,EAAE,sBAAsB,EAAE,CAAC;AAC3B,EAAE,YAAY,EAAE,EAAE;AAClB,EAAE,GAAG,oCAAoC;AACzC,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,yBAA0B,IAAG,CAAC,QAAQ,GAAmC,EAAE,KAAK;AAC7F,EAAE,MAAM,iCAAiC,WAAA;AACzC,MAAM,CAAC;AACP;AACA,SAAS,QAAQ,CAAC,2BAA2B,QAAQ,CAAC,cAAc;AACpE,OAAM;AACN,MAAM,KAAK,CAAA;AACX;AACA,EAAE,oBAAoB,EAAE,CAAA;AACxB;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,QAAQ,CAAC,2BAA2B,QAAQ,CAAC,cAAc,EAAE;AACpE;AACA,IAAI,QAAQ,CAAC,uBAAA,GAA0B,QAAQ,CAAC,cAAc,CAAA;AAC9D,GAAE;AACF;AACA,EAAE,MAAM,UAAU;AAClB,IAAI,GAAG,+BAA+B;AACtC,IAAI,GAAG,QAAQ;AACf,GAAG,CAAA;AACH;AACA,EAAE,MAAM,iBAAA,GAAoB,sBAAsB,EAAE,CAAA;AACpD;AACA;AACA,EAAE,MAAM,+BAA+B,GAAgC,EAAE,CAAA;AACzE,EAAE,IAAI,OAAO,CAAC,SAAS,EAAE;AACzB,IAAI,gBAAgB,CAAC,+BAA+B,EAAE,OAAO,CAAC,sBAAsB,CAAC,CAAA;AACrF,GAAE;AACF;AACA,EAAE,IAAI,OAAO,CAAC,cAAc,EAAE;AAC9B,IAAI,sBAAsB,EAAE,CAAA;AAC5B,GAAE;AACF,EAAE,IAAI,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE;AAC/C,IAAI,yBAAyB,EAAE,CAAA;AAC/B,GAAE;AACF;AACA,EAAE,MAAM,WAAW;;AAGjB,GAAI;AACN,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG,CAAA;AACH;AACA;AACA,EAAE,SAAS,uBAAuB,CAAC,OAAO,EAA+C;AACzF;AACA,IAAI,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AAC/B;AACA,IAAI,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAA,EAAoB,GAAE,OAAO,CAAA;AACrF;AACA,IAAI,MAAM,qBAAsB,GAAE,OAAO,CAAC,EAAA,KAAO,UAAU,CAAA;AAC3D;AACA,IAAI,IAAI,eAAe,CAAA;AACvB,IAAI,IAAI,qBAAqB,EAAE;AAC/B,MAAM,MAAM,WAAY,GAAE,qBAAsB,GAAE,cAAc,CAAC,cAAc,CAAE,GAAE,EAAE,CAAA;AACrF,MAAM,MAAM,OAAQ,GAAE,qBAAsB,GAAE,cAAc,CAAC,SAAS,CAAE,GAAE,SAAS,CAAA;AACnF,MAAM,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,YAAY,EAAE,OAAQ,EAAA,GAAI,6BAA6B,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;AACzG,MAAM,kBAAkB;AACxB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,aAAa,EAAE,OAAO;AAC9B,QAAQ,GAAG,OAAO;AAClB,QAAQ,QAAQ,EAAE;AAClB;AACA,UAAU,GAAG,OAAO,CAAC,QAAQ;AAC7B,UAAU,sBAAsB,EAAE,GAAG;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,IAAI;AACrB,OAAO,CAAA;AACP,WAAW;AACX,MAAM,kBAAkB;AACxB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,GAAG,OAAO;AAClB,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,MAAM,YAAa,GAAE,eAAgB,GAAE,eAAe,CAAC,eAAe,CAAE,GAAE,eAAe,CAAA;AAC7F;AACA;AACA;AACA,IAAI,YAAY,CAAC,QAAS;AAC1B,MAAM,YAAY,CAAC,IAAK,KAAI,eAAe,CAAC,IAAA;AAC5C;AACA,UAAU,EAAE,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAS,EAAA;AACvD;AACA,UAAU,YAAY,CAAC,QAAQ,CAAA;AAC/B;AACA,IAAI,WAAW,CAAC,IAAA,GAAO,YAAY,CAAC,IAAI,CAAA;AACxC,IAAI,WAAW,CAAC,OAAQ,GAAE,YAAY,CAAA;AACtC;AACA,IAAI,IAAI,YAAY,CAAC,OAAQ,KAAI,KAAK,EAAE;AACxC,MAAM,WAAY,IAAG,MAAM,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,YAAY,CAAC,EAAE,CAAC,uCAAuC,CAAC,CAAC,CAAA;AACpH,KAAI;AACJ;AACA,IAAI,WAAY,IAAG,MAAM,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,YAAY,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAA;AAC3F;AACA,IAAI,MAAM,EAAE,QAAS,EAAA,GAAI,MAAM,CAAA;AAC/B;AACA,IAAI,MAAM,eAAgB,GAAE,oBAAoB;AAChD,MAAM,GAAG;AACT,MAAM,YAAY;AAClB,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,IAAI;AACV,MAAM,EAAE,UAAU;AAClB,MAAM,iBAAiB;AACvB,MAAM,qBAAqB;AAC3B,KAAK,CAAA;AACL;AACA,IAAI,IAAI,qBAAA,IAAyB,MAAM,CAAC,QAAQ,EAAE;AAClD,MAAM,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;AACjE,QAAQ,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAE,UAAU,CAAC,EAAE;AAC/E,UAAU,eAAe,CAAC,oBAAoB,EAAE,CAAA;AAChD,SAAQ;AACR,OAAO,CAAC,CAAA;AACR;AACA,MAAM,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC5E,QAAQ,eAAe,CAAC,oBAAoB,EAAE,CAAA;AAC9C,OAAM;AACN,KAAI;AACJ;AACA,IAAI,eAAe,CAAC,4BAA4B,CAAC,eAAe;AAChE,MAAM,iBAAiB,EAAE,CAAA;AACzB,MAAM,qBAAqB,CAAC,WAAW,CAAC,CAAA;AACxC,KAAK,CAAC,CAAA;AACN;AACA,IAAI,OAAO,eAAgB,EAAA;AAC3B,GAAE;AACF;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,8BAA8B;AACxC;AACA,IAAI,SAAS,EAAE,MAAM,EAAE;AACvB,IAAI,aAAa,CAAC,MAAM,EAAE;AAC1B,MAAM,MAAM,aAAc,GAAE,MAAM,CAAC,UAAU,EAAE,CAAA;AAC/C;AACA,MAAM,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,QAAQ,EAAE,0BAA0B,EAAE,iBAAiB,EAAE,cAAe;AACtH,QAAQ,OAAO,CAAA;AACf;AACA,MAAM,MAAM,oCAAqC,GAAE,iBAAiB,aAAa,CAAC,uBAAuB,CAAA;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,uBAAwB,GAAE,wCAAwC,OAAO,CAAC,uBAAuB,CAAA;AAC7G,MAAM,IAAI,WAAA,IAAe,8BAA+B,IAAG,oCAAoC,EAAE;AACjG,QAAQ,MAAM,CAAC,IAAI;AACnB,UAAU,wKAAwK;AAClL,SAAS,CAAA;AACT,OAAM;AACN;AACA,MAAM,IAAI,UAAU,CAAA;AACpB,MAAM,IAAI,WAAW,GAAuB,MAAM,CAAC,QAAS,IAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAA;AACnF;AACA,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE;AACrB,QAAQ,MAAM,CAAC,EAAE,CAAC,qBAAqB,EAAE,CAAC,OAAO,KAAuB;AACxE,UAAU,IAAI,UAAU,EAAE;AAC1B,YAAY,eAAe,MAAM,CAAC,GAAG,CAAC,CAAC,iDAAiD,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAA,CAAA,CAAA;AACA;AACA,YAAA,UAAA,CAAA,GAAA,EAAA,CAAA;AACA,WAAA;AACA,UAAA,UAAA,GAAA,uBAAA,CAAA;AACA,YAAA,EAAA,EAAA,YAAA;AACA,YAAA,GAAA,OAAA;AACA,WAAA,CAAA,CAAA;AACA,SAAA,CAAA,CAAA;AACA;AACA,QAAA,MAAA,CAAA,EAAA,CAAA,mBAAA,EAAA,CAAA,OAAA,KAAA;AACA,UAAA,IAAA,UAAA,EAAA;AACA,YAAA,WAAA,IAAA,MAAA,CAAA,GAAA,CAAA,CAAA,iDAAA,EAAA,UAAA,CAAA,UAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA,YAAA,UAAA,CAAA,GAAA,EAAA,CAAA;AACA,WAAA;AACA,UAAA,UAAA,GAAA,uBAAA,CAAA;AACA,YAAA,EAAA,EAAA,UAAA;AACA,YAAA,GAAA,OAAA;AACA,WAAA,CAAA,CAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,IAAA,OAAA,CAAA,kBAAA,IAAA,MAAA,CAAA,IAAA,IAAA,MAAA,CAAA,QAAA,EAAA;AACA,QAAA,MAAA,OAAA,GAAA;AACA,UAAA,IAAA,EAAA,MAAA,CAAA,QAAA,CAAA,QAAA;AACA;AACA,UAAA,cAAA,EAAA,4BAAA,GAAA,4BAAA,GAAA,IAAA,GAAA,SAAA;AACA,UAAA,MAAA,EAAA,uBAAA;AACA,UAAA,UAAA,EAAA;AACA,YAAA,CAAA,gCAAA,GAAA,KAAA;AACA,WAAA;AACA,SAAA,CAAA;AACA,QAAA,+BAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,IAAA,OAAA,CAAA,oBAAA,IAAA,MAAA,CAAA,IAAA,IAAA,MAAA,CAAA,QAAA,EAAA;AACA,QAAA,gCAAA,CAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAA,IAAA,IAAA,KAAA,SAAA,IAAA,WAAA,IAAA,WAAA,CAAA,OAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACA,YAAA,WAAA,GAAA,SAAA,CAAA;AACA,YAAA,OAAA;AACA,WAAA;AACA;AACA,UAAA,IAAA,IAAA,KAAA,EAAA,EAAA;AACA,YAAA,WAAA,GAAA,SAAA,CAAA;AACA,YAAA,MAAA,OAAA,GAAA;AACA,cAAA,IAAA,EAAA,MAAA,CAAA,QAAA,CAAA,QAAA;AACA,cAAA,MAAA,EAAA,yBAAA;AACA,cAAA,UAAA,EAAA;AACA,gBAAA,CAAA,gCAAA,GAAA,KAAA;AACA,eAAA;AACA,aAAA,CAAA;AACA;AACA,YAAA,iCAAA,CAAA,MAAA,EAAA,OAAA,CAAA,CAAA;AACA,WAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,IAAA,kBAAA,EAAA;AACA,QAAA,8BAAA,EAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,IAAA,YAAA,CAAA,kBAAA,EAAA;AACA,QAAA,2BAAA,CAAA,OAAA,EAAA,WAAA,CAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,IAAA,OAAA,CAAA,SAAA,EAAA;AACA,QAAA,8BAAA,CAAA,+BAAA,EAAA,WAAA,CAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,0BAAA,CAAA;AACA,QAAA,UAAA;AACA,QAAA,QAAA;AACA,QAAA,uBAAA;AACA,QAAA,0BAAA;AACA,QAAA,iBAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA;AACA,IAAA,OAAA;AACA,GAAA,CAAA;AACA,CAAA,CAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,+BAAA,CAAA,MAAA,EAAA,WAAA,EAAA;AACA,EAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,CAAA,IAAA,CAAA,mBAAA,EAAA,WAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,IAAA,GAAA,aAAA,EAAA,CAAA;AACA,EAAA,MAAA,EAAA,GAAA,IAAA,IAAA,UAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;AACA,EAAA,OAAA,EAAA,KAAA,UAAA,GAAA,IAAA,GAAA,SAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,iCAAA,CAAA,MAAA,EAAA,WAAA,EAAA;AACA,EAAA,IAAA,CAAA,MAAA,CAAA,IAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,CAAA,IAAA,CAAA,qBAAA,EAAA,WAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,IAAA,GAAA,aAAA,EAAA,CAAA;AACA,EAAA,MAAA,EAAA,GAAA,IAAA,IAAA,UAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA;AACA,EAAA,OAAA,EAAA,KAAA,YAAA,GAAA,IAAA,GAAA,SAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,cAAA,CAAA,QAAA,EAAA;AACA;AACA;AACA;AACA,EAAA,MAAA,OAAA,GAAA,aAAA,CAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA,OAAA,GAAA,OAAA,CAAA,YAAA,CAAA,SAAA,CAAA,GAAA,SAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,2BAAA;AACA,EAAA,OAAA;AACA,EAAA,WAAA;;AAGA;AACA,EAAA;AACA,EAAA,IAAA,8BAAA,CAAA;AACA,EAAA,MAAA,8BAAA,GAAA,MAAA;AACA,IAAA,MAAA,EAAA,WAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,GAAA,OAAA,CAAA;AACA,IAAA,MAAA,EAAA,GAAA,iBAAA,CAAA;AACA;AACA;AACA,IAAA,MAAA,kBAAA,GAAA,oBAAA,EAAA,CAAA;AACA,IAAA,IAAA,kBAAA,IAAA,kBAAA,CAAA,EAAA,IAAA,CAAA,YAAA,EAAA,UAAA,CAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,EAAA,CAAA,EAAA;AACA,MAAA,WAAA;AACA,QAAA,MAAA,CAAA,IAAA;AACA,UAAA,CAAA,yBAAA,EAAA,EAAA,CAAA,yEAAA,CAAA;AACA,SAAA,CAAA;AACA,MAAA,OAAA,SAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,8BAAA,EAAA;AACA,MAAA,8BAAA,CAAA,eAAA,CAAA,wBAAA,CAAA,CAAA;AACA,MAAA,8BAAA,CAAA,GAAA,EAAA,CAAA;AACA,MAAA,8BAAA,GAAA,SAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,CAAA,WAAA,CAAA,IAAA,EAAA;AACA,MAAA,WAAA,IAAA,MAAA,CAAA,IAAA,CAAA,CAAA,yBAAA,EAAA,EAAA,CAAA,iDAAA,CAAA,CAAA,CAAA;AACA,MAAA,OAAA,SAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,EAAA,QAAA,EAAA,GAAA,MAAA,CAAA;AACA;AACA,IAAA,MAAA,OAAA,GAAA;AACA,MAAA,IAAA,EAAA,WAAA,CAAA,IAAA;AACA,MAAA,EAAA;AACA,MAAA,OAAA,EAAA,IAAA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,CAAA,gCAAA,GAAA,WAAA,CAAA,OAAA,GAAA,SAAA,CAAA,WAAA,CAAA,OAAA,CAAA,GAAA,KAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA;AACA,IAAA,8BAAA,GAAA,oBAAA;AACA;AACA,MAAA,aAAA,EAAA;AACA,MAAA,OAAA;AACA,MAAA,WAAA;AACA,MAAA,YAAA;AACA,MAAA,IAAA;AACA,MAAA,EAAA,QAAA,EAAA;AACA,MAAA,iBAAA;AACA,KAAA,CAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,CAAA,OAAA,CAAA,CAAA,OAAA,CAAA,IAAA,IAAA;AACA,IAAA,IAAA,MAAA,CAAA,QAAA,EAAA;AACA,MAAA,gBAAA,CAAA,IAAA,EAAA,8BAAA,EAAA,EAAA,IAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,wBAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,UAAA,IAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,MAAA,gBAAA,GAAA,EAAA,CAAA;AACA;AACA;AACA,SAAA,8BAAA;AACA,EAAA,+BAAA;AACA,EAAA,WAAA;;AAGA;AACA,EAAA;AACA,EAAA,MAAA,aAAA,GAAA,CAAA,EAAA,OAAA,EAAA,KAAA;AACA,IAAA,MAAA,MAAA,GAAA,SAAA,EAAA,CAAA;AACA;AACA;AACA,IAAA,MAAA,MAAA;AACA,MAAA,MAAA,KAAA,SAAA,IAAA,MAAA,CAAA,oBAAA,KAAA,SAAA;AACA,WAAA,MAAA,CAAA,oBAAA,CAAA,QAAA,CAAA;AACA,UAAA,SAAA,CAAA;AACA,IAAA,MAAA,QAAA,GAAA,MAAA,KAAA,SAAA,GAAA,MAAA,CAAA,WAAA,EAAA,GAAA,SAAA,CAAA;AACA;AACA,IAAA,MAAA,iBAAA,GAAA,oBAAA,EAAA,CAAA;AACA,IAAA,MAAA,YAAA,GAAA,eAAA,EAAA,CAAA;AACA,IAAA,MAAA,IAAA,GAAA,YAAA,KAAA,SAAA,GAAA,YAAA,CAAA,OAAA,EAAA,GAAA,SAAA,CAAA;AACA,IAAA,OAAA,CAAA,OAAA,CAAA,KAAA,IAAA;AACA,MAAA,IAAA,wBAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,MAAA,aAAA,GAAA,KAAA,CAAA,aAAA,CAAA;AACA,QAAA,IAAA,aAAA,KAAA,SAAA,EAAA;AACA,UAAA,OAAA;AACA,SAAA;AACA,QAAA,MAAA,mBAAA,GAAA,+BAAA,CAAA,aAAA,CAAA,CAAA;AACA,QAAA,MAAA,QAAA,GAAA,KAAA,CAAA,QAAA,CAAA;AACA,QAAA,MAAA,SAAA,GAAA,KAAA,CAAA,SAAA,CAAA;AACA,QAAA,MAAA,IAAA,GAAA,MAAA,CAAA,IAAA,CAAA,+BAAA,CAAA,CAAA;AACA,QAAA,MAAA,gBAAA;AACA,UAAA,IAAA,CAAA,MAAA,GAAA,CAAA;AACA,cAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA,KAAA;AACA,gBAAA,OAAA,+BAAA,CAAA,CAAA,CAAA,CAAA,QAAA,GAAA,+BAAA,CAAA,CAAA,CAAA,CAAA,QAAA;AACA,oBAAA,CAAA;AACA,oBAAA,CAAA,CAAA;AACA,eAAA,CAAA;AACA,cAAA,SAAA,CAAA;AACA;AACA;AACA,QAAA,IAAA,KAAA,CAAA,SAAA,KAAA,aAAA,EAAA;AACA,UAAA,MAAA,aAAA,GAAA,IAAA;AACA,aAAA,GAAA,CAAA,GAAA,IAAA,+BAAA,CAAA,GAAA,CAAA,CAAA;AACA,aAAA,IAAA,CAAA,WAAA,IAAA;AACA,cAAA,OAAA,WAAA,CAAA,QAAA,KAAA,QAAA,IAAA,WAAA,CAAA,SAAA,KAAA,SAAA,CAAA;AACA,aAAA,CAAA,CAAA;AACA,UAAA,IAAA,aAAA,EAAA;AACA,YAAA,OAAA;AACA,WAAA;AACA,SAAA;AACA;AACA,QAAA,IAAA,CAAA,aAAA,EAAA;AACA,UAAA,OAAA;AACA,SAAA;AACA;AACA,QAAA,IAAA,mBAAA,EAAA;AACA,UAAA,mBAAA,CAAA,QAAA,GAAA,IAAA,CAAA,GAAA,CAAA,mBAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA;AACA,SAAA,MAAA;AACA,UAAA,IAAA,CAAA,MAAA,GAAA,gBAAA;AACA,UAAA,gBAAA,KAAA,SAAA;AACA,UAAA,QAAA,GAAA,+BAAA,CAAA,gBAAA,CAAA,CAAA,QAAA;AACA,UAAA;AACA;AACA,UAAA,MAAA,SAAA,GAAA,WAAA,CAAA,IAAA,CAAA;AACA,UAAA,MAAA,aAAA,GAAA,WAAA,CAAA,OAAA,CAAA;AACA,UAAA,IAAA,SAAA,IAAA,aAAA,EAAA;AACA,YAAA,IAAA,gBAAA,IAAA,MAAA,CAAA,IAAA,CAAA,+BAAA,CAAA,CAAA,MAAA,IAAA,gBAAA,EAAA;AACA;AACA,cAAA,OAAA,+BAAA,CAAA,gBAAA,CAAA,CAAA;AACA,aAAA;AACA,YAAA,+BAAA,CAAA,aAAA,CAAA,GAAA;AACA,cAAA,SAAA;AACA,cAAA,QAAA;AACA,cAAA,aAAA;AACA,cAAA,IAAA;AACA,cAAA,iBAAA;AACA,cAAA,QAAA;AACA,cAAA,SAAA;AACA,aAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,CAAA;AACA,EAAA,oCAAA,CAAA,OAAA,EAAA,aAAA,CAAA,CAAA;AACA,EAAA,oCAAA,CAAA,aAAA,EAAA,aAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,SAAA,CAAA,OAAA,EAAA;AACA,EAAA,MAAA,oBAAA,GAAA,OAAA,CAAA,UAAA,IAAA,OAAA,CAAA,UAAA,CAAA,gCAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,cAAA,GAAA,OAAA,CAAA,IAAA,IAAA,OAAA,CAAA,IAAA,CAAA,gCAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,kBAAA,GAAA,OAAA,CAAA,QAAA,IAAA,OAAA,CAAA,QAAA,CAAA,MAAA,CAAA;AACA;AACA,EAAA,OAAA,oBAAA,IAAA,cAAA,IAAA,kBAAA,CAAA;AACA;;;;"}