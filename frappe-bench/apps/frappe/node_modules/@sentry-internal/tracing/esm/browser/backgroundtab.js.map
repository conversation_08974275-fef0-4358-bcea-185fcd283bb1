{"version": 3, "file": "backgroundtab.js", "sources": ["../../../src/browser/backgroundtab.ts"], "sourcesContent": ["import type { IdleTransaction, SpanStatusType } from '@sentry/core';\nimport { getActiveTransaction, spanToJSON } from '@sentry/core';\nimport { logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../common/debug-build';\nimport { WINDOW } from './types';\n\n/**\n * Add a listener that cancels and finishes a transaction when the global\n * document is hidden.\n */\nexport function registerBackgroundTabDetection(): void {\n  if (WINDOW.document) {\n    WINDOW.document.addEventListener('visibilitychange', () => {\n      // eslint-disable-next-line deprecation/deprecation\n      const activeTransaction = getActiveTransaction() as IdleTransaction;\n      if (WINDOW.document!.hidden && activeTransaction) {\n        const statusType: SpanStatusType = 'cancelled';\n\n        const { op, status } = spanToJSON(activeTransaction);\n\n        DEBUG_BUILD &&\n          logger.log(`[Tracing] Transaction: ${statusType} -> since tab moved to the background, op: ${op}`);\n        // We should not set status if it is already set, this prevent important statuses like\n        // error or data loss from being overwritten on transaction.\n        if (!status) {\n          activeTransaction.setStatus(statusType);\n        }\n        // TODO: Can we rewrite this to an attribute?\n        // eslint-disable-next-line deprecation/deprecation\n        activeTransaction.setTag('visibilitychange', 'document.hidden');\n        activeTransaction.end();\n      }\n    });\n  } else {\n    DEBUG_BUILD && logger.warn('[Tracing] Could not set up background tab detection due to lack of global document');\n  }\n}\n"], "names": [], "mappings": ";;;;;AAOA;AACA;AACA;AACA;AACO,SAAS,8BAA8B,GAAS;AACvD,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;AAC/D;AACA,MAAM,MAAM,iBAAA,GAAoB,oBAAoB,EAAG,EAAA;AACvD,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAE,MAAA,IAAU,iBAAiB,EAAE;AACxD,QAAQ,MAAM,UAAU,GAAmB,WAAW,CAAA;AACtD;AACA,QAAQ,MAAM,EAAE,EAAE,EAAE,MAAA,KAAW,UAAU,CAAC,iBAAiB,CAAC,CAAA;AAC5D;AACA,QAAQ,WAAY;AACpB,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU,CAAC,2CAA2C,EAAE,EAAE,CAAC,CAAA,CAAA,CAAA;AACA;AACA;AACA,QAAA,IAAA,CAAA,MAAA,EAAA;AACA,UAAA,iBAAA,CAAA,SAAA,CAAA,UAAA,CAAA,CAAA;AACA,SAAA;AACA;AACA;AACA,QAAA,iBAAA,CAAA,MAAA,CAAA,kBAAA,EAAA,iBAAA,CAAA,CAAA;AACA,QAAA,iBAAA,CAAA,GAAA,EAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,WAAA,IAAA,MAAA,CAAA,IAAA,CAAA,oFAAA,CAAA,CAAA;AACA,GAAA;AACA;;;;"}