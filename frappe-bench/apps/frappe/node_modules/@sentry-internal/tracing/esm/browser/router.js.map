{"version": 3, "file": "router.js", "sources": ["../../../src/browser/router.ts"], "sourcesContent": ["import type { Transaction, TransactionContext } from '@sentry/types';\nimport { addHistoryInstrumentationHandler, browserPerformanceTimeOrigin, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../common/debug-build';\nimport { WINDOW } from './types';\n\n/**\n * Default function implementing pageload and navigation transactions\n */\nexport function instrumentRoutingWithDefaults<T extends Transaction>(\n  customStartTransaction: (context: TransactionContext) => T | undefined,\n  startTransactionOnPageLoad: boolean = true,\n  startTransactionOnLocationChange: boolean = true,\n): void {\n  if (!WINDOW || !WINDOW.location) {\n    DEBUG_BUILD && logger.warn('Could not initialize routing instrumentation due to invalid location');\n    return;\n  }\n\n  let startingUrl: string | undefined = WINDOW.location.href;\n\n  let activeTransaction: T | undefined;\n  if (startTransactionOnPageLoad) {\n    activeTransaction = customStartTransaction({\n      name: WINDOW.location.pathname,\n      // pageload should always start at timeOrigin (and needs to be in s, not ms)\n      startTimestamp: browserPerformanceTimeOrigin ? browserPerformanceTimeOrigin / 1000 : undefined,\n      op: 'pageload',\n      origin: 'auto.pageload.browser',\n      metadata: { source: 'url' },\n    });\n  }\n\n  if (startTransactionOnLocationChange) {\n    addHistoryInstrumentationHandler(({ to, from }) => {\n      /**\n       * This early return is there to account for some cases where a navigation transaction starts right after\n       * long-running pageload. We make sure that if `from` is undefined and a valid `startingURL` exists, we don't\n       * create an uneccessary navigation transaction.\n       *\n       * This was hard to duplicate, but this behavior stopped as soon as this fix was applied. This issue might also\n       * only be caused in certain development environments where the usage of a hot module reloader is causing\n       * errors.\n       */\n      if (from === undefined && startingUrl && startingUrl.indexOf(to) !== -1) {\n        startingUrl = undefined;\n        return;\n      }\n\n      if (from !== to) {\n        startingUrl = undefined;\n        if (activeTransaction) {\n          DEBUG_BUILD && logger.log(`[Tracing] Finishing current transaction with op: ${activeTransaction.op}`);\n          // If there's an open transaction on the scope, we need to finish it before creating an new one.\n          activeTransaction.end();\n        }\n        activeTransaction = customStartTransaction({\n          name: WINDOW.location.pathname,\n          op: 'navigation',\n          origin: 'auto.navigation.browser',\n          metadata: { source: 'url' },\n        });\n      }\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;AAMA;AACA;AACA;AACO,SAAS,6BAA6B;AAC7C,EAAE,sBAAsB;AACxB,EAAE,0BAA0B,GAAY,IAAI;AAC5C,EAAE,gCAAgC,GAAY,IAAI;AAClD,EAAQ;AACR,EAAE,IAAI,CAAC,MAAA,IAAU,CAAC,MAAM,CAAC,QAAQ,EAAE;AACnC,IAAI,eAAe,MAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAA;AACtG,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAE,IAAI,WAAW,GAAuB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAA;AAC5D;AACA,EAAE,IAAI,iBAAiB,CAAA;AACvB,EAAE,IAAI,0BAA0B,EAAE;AAClC,IAAI,iBAAA,GAAoB,sBAAsB,CAAC;AAC/C,MAAM,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;AACpC;AACA,MAAM,cAAc,EAAE,4BAA6B,GAAE,+BAA+B,IAAA,GAAO,SAAS;AACpG,MAAM,EAAE,EAAE,UAAU;AACpB,MAAM,MAAM,EAAE,uBAAuB;AACrC,MAAM,QAAQ,EAAE,EAAE,MAAM,EAAE,OAAO;AACjC,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA,EAAE,IAAI,gCAAgC,EAAE;AACxC,IAAI,gCAAgC,CAAC,CAAC,EAAE,EAAE,EAAE,IAAA,EAAM,KAAK;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,IAAA,KAAS,SAAA,IAAa,WAAY,IAAG,WAAW,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE;AAC/E,QAAQ,WAAA,GAAc,SAAS,CAAA;AAC/B,QAAQ,OAAM;AACd,OAAM;AACN;AACA,MAAM,IAAI,IAAK,KAAI,EAAE,EAAE;AACvB,QAAQ,WAAA,GAAc,SAAS,CAAA;AAC/B,QAAQ,IAAI,iBAAiB,EAAE;AAC/B,UAAU,WAAY,IAAG,MAAM,CAAC,GAAG,CAAC,CAAC,iDAAiD,EAAE,iBAAiB,CAAC,EAAE,CAAC,CAAA,CAAA,CAAA;AACA;AACA,UAAA,iBAAA,CAAA,GAAA,EAAA,CAAA;AACA,SAAA;AACA,QAAA,iBAAA,GAAA,sBAAA,CAAA;AACA,UAAA,IAAA,EAAA,MAAA,CAAA,QAAA,CAAA,QAAA;AACA,UAAA,EAAA,EAAA,YAAA;AACA,UAAA,MAAA,EAAA,yBAAA;AACA,UAAA,QAAA,EAAA,EAAA,MAAA,EAAA,KAAA,EAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;;;;"}