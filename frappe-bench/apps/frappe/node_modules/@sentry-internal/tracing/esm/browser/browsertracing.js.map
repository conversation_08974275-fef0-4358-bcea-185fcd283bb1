{"version": 3, "file": "browsertracing.js", "sources": ["../../../src/browser/browsertracing.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { Hub, IdleTransaction } from '@sentry/core';\nimport { getClient, getCurrentScope } from '@sentry/core';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  TRACING_DEFAULTS,\n  addTracingExtensions,\n  getActiveTransaction,\n  startIdleTransaction,\n} from '@sentry/core';\nimport type { EventProcessor, Integration, Transaction, TransactionContext, TransactionSource } from '@sentry/types';\nimport { getDomElement, logger, propagationContextFromHeaders } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../common/debug-build';\nimport { registerBackgroundTabDetection } from './backgroundtab';\nimport { addPerformanceInstrumentationHandler } from './instrument';\nimport {\n  addPerformanceEntries,\n  startTrackingINP,\n  startTrackingInteractions,\n  startTrackingLongTasks,\n  startTrackingWebVitals,\n} from './metrics';\nimport type { RequestInstrumentationOptions } from './request';\nimport { defaultRequestInstrumentationOptions, instrumentOutgoingRequests } from './request';\nimport { instrumentRoutingWithDefaults } from './router';\nimport { WINDOW } from './types';\nimport type { InteractionRouteNameMapping } from './web-vitals/types';\n\nexport const BROWSER_TRACING_INTEGRATION_ID = 'BrowserTracing';\n\n/** Options for Browser Tracing integration */\nexport interface BrowserTracingOptions extends RequestInstrumentationOptions {\n  /**\n   * The time to wait in ms until the transaction will be finished during an idle state. An idle state is defined\n   * by a moment where there are no in-progress spans.\n   *\n   * The transaction will use the end timestamp of the last finished span as the endtime for the transaction.\n   * If there are still active spans when this the `idleTimeout` is set, the `idleTimeout` will get reset.\n   * Time is in ms.\n   *\n   * Default: 1000\n   */\n  idleTimeout: number;\n\n  /**\n   * The max duration for a transaction. If a transaction duration hits the `finalTimeout` value, it\n   * will be finished.\n   * Time is in ms.\n   *\n   * Default: 30000\n   */\n  finalTimeout: number;\n\n  /**\n   * The heartbeat interval. If no new spans are started or open spans are finished within 3 heartbeats,\n   * the transaction will be finished.\n   * Time is in ms.\n   *\n   * Default: 5000\n   */\n  heartbeatInterval: number;\n\n  /**\n   * Flag to enable/disable creation of `navigation` transaction on history changes.\n   *\n   * Default: true\n   */\n  startTransactionOnLocationChange: boolean;\n\n  /**\n   * Flag to enable/disable creation of `pageload` transaction on first pageload.\n   *\n   * Default: true\n   */\n  startTransactionOnPageLoad: boolean;\n\n  /**\n   * Flag Transactions where tabs moved to background with \"cancelled\". Browser background tab timing is\n   * not suited towards doing precise measurements of operations. By default, we recommend that this option\n   * be enabled as background transactions can mess up your statistics in nondeterministic ways.\n   *\n   * Default: true\n   */\n  markBackgroundTransactions: boolean;\n\n  /**\n   * If true, Sentry will capture long tasks and add them to the corresponding transaction.\n   *\n   * Default: true\n   */\n  enableLongTask: boolean;\n\n  /**\n   * If true, Sentry will capture INP web vitals as standalone spans .\n   *\n   * Default: false\n   */\n  enableInp: boolean;\n\n  /**\n   * Sample rate to determine interaction span sampling.\n   * interactionsSampleRate is applied on top of the global tracesSampleRate.\n   * ie a tracesSampleRate of 0.1 and interactionsSampleRate of 0.5 will result in a 0.05 sample rate for interactions.\n   *\n   * Default: 1\n   */\n  interactionsSampleRate: number;\n\n  /**\n   * _metricOptions allows the user to send options to change how metrics are collected.\n   *\n   * _metricOptions is currently experimental.\n   *\n   * Default: undefined\n   */\n  _metricOptions?: Partial<{\n    /**\n     * @deprecated This property no longer has any effect and will be removed in v8.\n     */\n    _reportAllChanges: boolean;\n  }>;\n\n  /**\n   * _experiments allows the user to send options to define how this integration works.\n   * Note that the `enableLongTask` options is deprecated in favor of the option at the top level, and will be removed in v8.\n   *\n   * TODO (v8): Remove enableLongTask\n   *\n   * Default: undefined\n   */\n  _experiments: Partial<{\n    enableLongTask: boolean;\n    enableInteractions: boolean;\n    // eslint-disable-next-line deprecation/deprecation\n    onStartRouteTransaction: (t: Transaction | undefined, ctx: TransactionContext, getCurrentHub: () => Hub) => void;\n  }>;\n\n  /**\n   * beforeNavigate is called before a pageload/navigation transaction is created and allows users to modify transaction\n   * context data, or drop the transaction entirely (by setting `sampled = false` in the context).\n   *\n   * Note: For legacy reasons, transactions can also be dropped by returning `undefined`.\n   *\n   * @param context: The context data which will be passed to `startTransaction` by default\n   *\n   * @returns A (potentially) modified context object, with `sampled = false` if the transaction should be dropped.\n   */\n  beforeNavigate?(this: void, context: TransactionContext): TransactionContext | undefined;\n\n  /**\n   * Instrumentation that creates routing change transactions. By default creates\n   * pageload and navigation transactions.\n   */\n  routingInstrumentation<T extends Transaction>(\n    this: void,\n    customStartTransaction: (context: TransactionContext) => T | undefined,\n    startTransactionOnPageLoad?: boolean,\n    startTransactionOnLocationChange?: boolean,\n  ): void;\n}\n\nconst DEFAULT_BROWSER_TRACING_OPTIONS: BrowserTracingOptions = {\n  ...TRACING_DEFAULTS,\n  markBackgroundTransactions: true,\n  routingInstrumentation: instrumentRoutingWithDefaults,\n  startTransactionOnLocationChange: true,\n  startTransactionOnPageLoad: true,\n  enableLongTask: true,\n  enableInp: false,\n  interactionsSampleRate: 1,\n  _experiments: {},\n  ...defaultRequestInstrumentationOptions,\n};\n\n/** We store up to 10 interaction candidates max to cap memory usage. This is the same cap as getINP from web-vitals */\nconst MAX_INTERACTIONS = 10;\n\n/**\n * The Browser Tracing integration automatically instruments browser pageload/navigation\n * actions as transactions, and captures requests, metrics and errors as spans.\n *\n * The integration can be configured with a variety of options, and can be extended to use\n * any routing library. This integration uses {@see IdleTransaction} to create transactions.\n *\n * @deprecated Use `browserTracingIntegration()` instead.\n */\nexport class BrowserTracing implements Integration {\n  // This class currently doesn't have a static `id` field like the other integration classes, because it prevented\n  // @sentry/tracing from being treeshaken. Tree shakers do not like static fields, because they behave like side effects.\n  // TODO: Come up with a better plan, than using static fields on integration classes, and use that plan on all\n  // integrations.\n\n  /** Browser Tracing integration options */\n  public options: BrowserTracingOptions;\n\n  /**\n   * @inheritDoc\n   */\n  public name: string;\n\n  // eslint-disable-next-line deprecation/deprecation\n  private _getCurrentHub?: () => Hub;\n\n  private _collectWebVitals: () => void;\n\n  private _hasSetTracePropagationTargets: boolean;\n  private _interactionIdToRouteNameMapping: InteractionRouteNameMapping;\n  private _latestRoute: {\n    name: string | undefined;\n    context: TransactionContext | undefined;\n  };\n\n  public constructor(_options?: Partial<BrowserTracingOptions>) {\n    this.name = BROWSER_TRACING_INTEGRATION_ID;\n    this._hasSetTracePropagationTargets = false;\n\n    addTracingExtensions();\n\n    if (DEBUG_BUILD) {\n      this._hasSetTracePropagationTargets = !!(\n        _options &&\n        // eslint-disable-next-line deprecation/deprecation\n        (_options.tracePropagationTargets || _options.tracingOrigins)\n      );\n    }\n\n    this.options = {\n      ...DEFAULT_BROWSER_TRACING_OPTIONS,\n      ..._options,\n    };\n\n    // Special case: enableLongTask can be set in _experiments\n    // TODO (v8): Remove this in v8\n    if (this.options._experiments.enableLongTask !== undefined) {\n      this.options.enableLongTask = this.options._experiments.enableLongTask;\n    }\n\n    // TODO (v8): remove this block after tracingOrigins is removed\n    // Set tracePropagationTargets to tracingOrigins if specified by the user\n    // In case both are specified, tracePropagationTargets takes precedence\n    // eslint-disable-next-line deprecation/deprecation\n    if (_options && !_options.tracePropagationTargets && _options.tracingOrigins) {\n      // eslint-disable-next-line deprecation/deprecation\n      this.options.tracePropagationTargets = _options.tracingOrigins;\n    }\n\n    this._collectWebVitals = startTrackingWebVitals();\n    /** Stores a mapping of interactionIds from PerformanceEventTimings to the origin interaction path */\n    this._interactionIdToRouteNameMapping = {};\n\n    if (this.options.enableInp) {\n      startTrackingINP(this._interactionIdToRouteNameMapping, this.options.interactionsSampleRate);\n    }\n    if (this.options.enableLongTask) {\n      startTrackingLongTasks();\n    }\n    if (this.options._experiments.enableInteractions) {\n      startTrackingInteractions();\n    }\n\n    this._latestRoute = {\n      name: undefined,\n      context: undefined,\n    };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    this._getCurrentHub = getCurrentHub;\n    const hub = getCurrentHub();\n    // eslint-disable-next-line deprecation/deprecation\n    const client = hub.getClient();\n    const clientOptions = client && client.getOptions();\n\n    const {\n      routingInstrumentation: instrumentRouting,\n      startTransactionOnLocationChange,\n      startTransactionOnPageLoad,\n      markBackgroundTransactions,\n      traceFetch,\n      traceXHR,\n      shouldCreateSpanForRequest,\n      enableHTTPTimings,\n      _experiments,\n    } = this.options;\n\n    const clientOptionsTracePropagationTargets = clientOptions && clientOptions.tracePropagationTargets;\n    // There are three ways to configure tracePropagationTargets:\n    // 1. via top level client option `tracePropagationTargets`\n    // 2. via BrowserTracing option `tracePropagationTargets`\n    // 3. via BrowserTracing option `tracingOrigins` (deprecated)\n    //\n    // To avoid confusion, favour top level client option `tracePropagationTargets`, and fallback to\n    // BrowserTracing option `tracePropagationTargets` and then `tracingOrigins` (deprecated).\n    // This is done as it minimizes bundle size (we don't have to have undefined checks).\n    //\n    // If both 1 and either one of 2 or 3 are set (from above), we log out a warning.\n    // eslint-disable-next-line deprecation/deprecation\n    const tracePropagationTargets = clientOptionsTracePropagationTargets || this.options.tracePropagationTargets;\n    if (DEBUG_BUILD && this._hasSetTracePropagationTargets && clientOptionsTracePropagationTargets) {\n      logger.warn(\n        '[Tracing] The `tracePropagationTargets` option was set in the BrowserTracing integration and top level `Sentry.init`. The top level `Sentry.init` value is being used.',\n      );\n    }\n\n    instrumentRouting(\n      (context: TransactionContext) => {\n        const transaction = this._createRouteTransaction(context);\n\n        this.options._experiments.onStartRouteTransaction &&\n          this.options._experiments.onStartRouteTransaction(transaction, context, getCurrentHub);\n\n        return transaction;\n      },\n      startTransactionOnPageLoad,\n      startTransactionOnLocationChange,\n    );\n\n    if (markBackgroundTransactions) {\n      registerBackgroundTabDetection();\n    }\n\n    if (_experiments.enableInteractions) {\n      this._registerInteractionListener();\n    }\n\n    if (this.options.enableInp) {\n      this._registerInpInteractionListener();\n    }\n\n    instrumentOutgoingRequests({\n      traceFetch,\n      traceXHR,\n      tracePropagationTargets,\n      shouldCreateSpanForRequest,\n      enableHTTPTimings,\n    });\n  }\n\n  /** Create routing idle transaction. */\n  private _createRouteTransaction(context: TransactionContext): Transaction | undefined {\n    if (!this._getCurrentHub) {\n      DEBUG_BUILD &&\n        logger.warn(`[Tracing] Did not create ${context.op} transaction because _getCurrentHub is invalid.`);\n      return undefined;\n    }\n\n    const hub = this._getCurrentHub();\n\n    const { beforeNavigate, idleTimeout, finalTimeout, heartbeatInterval } = this.options;\n\n    const isPageloadTransaction = context.op === 'pageload';\n\n    let expandedContext: TransactionContext;\n    if (isPageloadTransaction) {\n      const sentryTrace = isPageloadTransaction ? getMetaContent('sentry-trace') : '';\n      const baggage = isPageloadTransaction ? getMetaContent('baggage') : undefined;\n      const { traceId, dsc, parentSpanId, sampled } = propagationContextFromHeaders(sentryTrace, baggage);\n      expandedContext = {\n        traceId,\n        parentSpanId,\n        parentSampled: sampled,\n        ...context,\n        metadata: {\n          // eslint-disable-next-line deprecation/deprecation\n          ...context.metadata,\n          dynamicSamplingContext: dsc,\n        },\n        trimEnd: true,\n      };\n    } else {\n      expandedContext = {\n        trimEnd: true,\n        ...context,\n      };\n    }\n\n    const modifiedContext = typeof beforeNavigate === 'function' ? beforeNavigate(expandedContext) : expandedContext;\n\n    // For backwards compatibility reasons, beforeNavigate can return undefined to \"drop\" the transaction (prevent it\n    // from being sent to Sentry).\n    const finalContext = modifiedContext === undefined ? { ...expandedContext, sampled: false } : modifiedContext;\n\n    // If `beforeNavigate` set a custom name, record that fact\n    // eslint-disable-next-line deprecation/deprecation\n    finalContext.metadata =\n      finalContext.name !== expandedContext.name\n        ? // eslint-disable-next-line deprecation/deprecation\n          { ...finalContext.metadata, source: 'custom' }\n        : // eslint-disable-next-line deprecation/deprecation\n          finalContext.metadata;\n\n    this._latestRoute.name = finalContext.name;\n    this._latestRoute.context = finalContext;\n\n    // eslint-disable-next-line deprecation/deprecation\n    if (finalContext.sampled === false) {\n      DEBUG_BUILD && logger.log(`[Tracing] Will not send ${finalContext.op} transaction because of beforeNavigate.`);\n    }\n\n    DEBUG_BUILD && logger.log(`[Tracing] Starting ${finalContext.op} transaction on scope`);\n\n    const { location } = WINDOW;\n\n    const idleTransaction = startIdleTransaction(\n      hub,\n      finalContext,\n      idleTimeout,\n      finalTimeout,\n      true,\n      { location }, // for use in the tracesSampler\n      heartbeatInterval,\n      isPageloadTransaction, // should wait for finish signal if it's a pageload transaction\n    );\n\n    if (isPageloadTransaction) {\n      if (WINDOW.document) {\n        WINDOW.document.addEventListener('readystatechange', () => {\n          if (['interactive', 'complete'].includes(WINDOW.document!.readyState)) {\n            idleTransaction.sendAutoFinishSignal();\n          }\n        });\n\n        if (['interactive', 'complete'].includes(WINDOW.document.readyState)) {\n          idleTransaction.sendAutoFinishSignal();\n        }\n      }\n    }\n\n    idleTransaction.registerBeforeFinishCallback(transaction => {\n      this._collectWebVitals();\n      addPerformanceEntries(transaction);\n    });\n\n    return idleTransaction as Transaction;\n  }\n\n  /** Start listener for interaction transactions */\n  private _registerInteractionListener(): void {\n    let inflightInteractionTransaction: IdleTransaction | undefined;\n    const registerInteractionTransaction = (): void => {\n      const { idleTimeout, finalTimeout, heartbeatInterval } = this.options;\n      const op = 'ui.action.click';\n\n      // eslint-disable-next-line deprecation/deprecation\n      const currentTransaction = getActiveTransaction();\n      if (currentTransaction && currentTransaction.op && ['navigation', 'pageload'].includes(currentTransaction.op)) {\n        DEBUG_BUILD &&\n          logger.warn(\n            `[Tracing] Did not create ${op} transaction because a pageload or navigation transaction is in progress.`,\n          );\n        return undefined;\n      }\n\n      if (inflightInteractionTransaction) {\n        inflightInteractionTransaction.setFinishReason('interactionInterrupted');\n        inflightInteractionTransaction.end();\n        inflightInteractionTransaction = undefined;\n      }\n\n      if (!this._getCurrentHub) {\n        DEBUG_BUILD && logger.warn(`[Tracing] Did not create ${op} transaction because _getCurrentHub is invalid.`);\n        return undefined;\n      }\n\n      if (!this._latestRoute.name) {\n        DEBUG_BUILD && logger.warn(`[Tracing] Did not create ${op} transaction because _latestRouteName is missing.`);\n        return undefined;\n      }\n\n      const hub = this._getCurrentHub();\n      const { location } = WINDOW;\n\n      const context: TransactionContext = {\n        name: this._latestRoute.name,\n        op,\n        trimEnd: true,\n        data: {\n          [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: this._latestRoute.context\n            ? getSource(this._latestRoute.context)\n            : undefined || 'url',\n        },\n      };\n\n      inflightInteractionTransaction = startIdleTransaction(\n        hub,\n        context,\n        idleTimeout,\n        finalTimeout,\n        true,\n        { location }, // for use in the tracesSampler\n        heartbeatInterval,\n      );\n    };\n\n    ['click'].forEach(type => {\n      if (WINDOW.document) {\n        addEventListener(type, registerInteractionTransaction, { once: false, capture: true });\n      }\n    });\n  }\n\n  /** Creates a listener on interaction entries, and maps interactionIds to the origin path of the interaction */\n  private _registerInpInteractionListener(): void {\n    const handleEntries = ({ entries }: { entries: PerformanceEntry[] }): void => {\n      const client = getClient();\n      // We need to get the replay, user, and activeTransaction from the current scope\n      // so that we can associate replay id, profile id, and a user display to the span\n      const replay =\n        client !== undefined && client.getIntegrationByName !== undefined\n          ? (client.getIntegrationByName('Replay') as Integration & { getReplayId: () => string })\n          : undefined;\n      const replayId = replay !== undefined ? replay.getReplayId() : undefined;\n      // eslint-disable-next-line deprecation/deprecation\n      const activeTransaction = getActiveTransaction();\n      const currentScope = getCurrentScope();\n      const user = currentScope !== undefined ? currentScope.getUser() : undefined;\n      entries.forEach(entry => {\n        if (isPerformanceEventTiming(entry)) {\n          const interactionId = entry.interactionId;\n          if (interactionId === undefined) {\n            return;\n          }\n          const existingInteraction = this._interactionIdToRouteNameMapping[interactionId];\n          const duration = entry.duration;\n          const startTime = entry.startTime;\n          const keys = Object.keys(this._interactionIdToRouteNameMapping);\n          const minInteractionId =\n            keys.length > 0\n              ? keys.reduce((a, b) => {\n                  return this._interactionIdToRouteNameMapping[a].duration <\n                    this._interactionIdToRouteNameMapping[b].duration\n                    ? a\n                    : b;\n                })\n              : undefined;\n          // For a first input event to be considered, we must check that an interaction event does not already exist with the same duration and start time.\n          // This is also checked in the web-vitals library.\n          if (entry.entryType === 'first-input') {\n            const matchingEntry = keys\n              .map(key => this._interactionIdToRouteNameMapping[key])\n              .some(interaction => {\n                return interaction.duration === duration && interaction.startTime === startTime;\n              });\n            if (matchingEntry) {\n              return;\n            }\n          }\n          // Interactions with an id of 0 and are not first-input are not valid.\n          if (!interactionId) {\n            return;\n          }\n          // If the interaction already exists, we want to use the duration of the longest entry, since that is what the INP metric uses.\n          if (existingInteraction) {\n            existingInteraction.duration = Math.max(existingInteraction.duration, duration);\n          } else if (\n            keys.length < MAX_INTERACTIONS ||\n            minInteractionId === undefined ||\n            duration > this._interactionIdToRouteNameMapping[minInteractionId].duration\n          ) {\n            // If the interaction does not exist, we want to add it to the mapping if there is space, or if the duration is longer than the shortest entry.\n            const routeName = this._latestRoute.name;\n            const parentContext = this._latestRoute.context;\n            if (routeName && parentContext) {\n              if (minInteractionId && Object.keys(this._interactionIdToRouteNameMapping).length >= MAX_INTERACTIONS) {\n                // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n                delete this._interactionIdToRouteNameMapping[minInteractionId];\n              }\n              this._interactionIdToRouteNameMapping[interactionId] = {\n                routeName,\n                duration,\n                parentContext,\n                user,\n                activeTransaction,\n                replayId,\n                startTime,\n              };\n            }\n          }\n        }\n      });\n    };\n    addPerformanceInstrumentationHandler('event', handleEntries);\n    addPerformanceInstrumentationHandler('first-input', handleEntries);\n  }\n}\n\n/** Returns the value of a meta tag */\nexport function getMetaContent(metaName: string): string | undefined {\n  // Can't specify generic to `getDomElement` because tracing can be used\n  // in a variety of environments, have to disable `no-unsafe-member-access`\n  // as a result.\n  const metaTag = getDomElement(`meta[name=${metaName}]`);\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return metaTag ? metaTag.getAttribute('content') : undefined;\n}\n\nfunction getSource(context: TransactionContext): TransactionSource | undefined {\n  const sourceFromAttributes = context.attributes && context.attributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n  // eslint-disable-next-line deprecation/deprecation\n  const sourceFromData = context.data && context.data[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n  // eslint-disable-next-line deprecation/deprecation\n  const sourceFromMetadata = context.metadata && context.metadata.source;\n\n  return sourceFromAttributes || sourceFromData || sourceFromMetadata;\n}\n\nfunction isPerformanceEventTiming(entry: PerformanceEntry): entry is PerformanceEventTiming {\n  return 'duration' in entry;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AA6BO,MAAM,8BAA+B,GAAE,iBAAgB;AAC9D;AACA;;AAmIA,MAAM,+BAA+B,GAA0B;AAC/D,EAAE,GAAG,gBAAgB;AACrB,EAAE,0BAA0B,EAAE,IAAI;AAClC,EAAE,sBAAsB,EAAE,6BAA6B;AACvD,EAAE,gCAAgC,EAAE,IAAI;AACxC,EAAE,0BAA0B,EAAE,IAAI;AAClC,EAAE,cAAc,EAAE,IAAI;AACtB,EAAE,SAAS,EAAE,KAAK;AAClB,EAAE,sBAAsB,EAAE,CAAC;AAC3B,EAAE,YAAY,EAAE,EAAE;AAClB,EAAE,GAAG,oCAAoC;AACzC,CAAC,CAAA;AACD;AACA;AACA,MAAM,gBAAA,GAAmB,EAAE,CAAA;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,gBAAsC;AACnD;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;;AAYA,GAAS,WAAW,CAAC,QAAQ,EAAmC;AAChE,IAAI,IAAI,CAAC,IAAK,GAAE,8BAA8B,CAAA;AAC9C,IAAI,IAAI,CAAC,8BAA+B,GAAE,KAAK,CAAA;AAC/C;AACA,IAAI,oBAAoB,EAAE,CAAA;AAC1B;AACA,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,IAAI,CAAC,8BAA+B,GAAE,CAAC;AAC7C,QAAQ,QAAS;AACjB;AACA,SAAS,QAAQ,CAAC,2BAA2B,QAAQ,CAAC,cAAc,CAAA;AACpE,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,OAAA,GAAU;AACnB,MAAM,GAAG,+BAA+B;AACxC,MAAM,GAAG,QAAQ;AACjB,KAAK,CAAA;AACL;AACA;AACA;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAA,KAAmB,SAAS,EAAE;AAChE,MAAM,IAAI,CAAC,OAAO,CAAC,cAAe,GAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,cAAc,CAAA;AAC5E,KAAI;AACJ;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,QAAS,IAAG,CAAC,QAAQ,CAAC,uBAAA,IAA2B,QAAQ,CAAC,cAAc,EAAE;AAClF;AACA,MAAM,IAAI,CAAC,OAAO,CAAC,0BAA0B,QAAQ,CAAC,cAAc,CAAA;AACpE,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,iBAAA,GAAoB,sBAAsB,EAAE,CAAA;AACrD;AACA,IAAI,IAAI,CAAC,gCAAiC,GAAE,EAAE,CAAA;AAC9C;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAChC,MAAM,gBAAgB,CAAC,IAAI,CAAC,gCAAgC,EAAE,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAA;AAClG,KAAI;AACJ,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;AACrC,MAAM,sBAAsB,EAAE,CAAA;AAC9B,KAAI;AACJ,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE;AACtD,MAAM,yBAAyB,EAAE,CAAA;AACjC,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,YAAA,GAAe;AACxB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,OAAO,EAAE,SAAS;AACxB,KAAK,CAAA;AACL,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,CAAC,EAAsC,aAAa,EAAmB;AAC1F,IAAI,IAAI,CAAC,cAAe,GAAE,aAAa,CAAA;AACvC,IAAI,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AAC/B;AACA,IAAI,MAAM,MAAO,GAAE,GAAG,CAAC,SAAS,EAAE,CAAA;AAClC,IAAI,MAAM,gBAAgB,MAAA,IAAU,MAAM,CAAC,UAAU,EAAE,CAAA;AACvD;AACA,IAAI,MAAM;AACV,MAAM,sBAAsB,EAAE,iBAAiB;AAC/C,MAAM,gCAAgC;AACtC,MAAM,0BAA0B;AAChC,MAAM,0BAA0B;AAChC,MAAM,UAAU;AAChB,MAAM,QAAQ;AACd,MAAM,0BAA0B;AAChC,MAAM,iBAAiB;AACvB,MAAM,YAAY;AAClB,KAAM,GAAE,IAAI,CAAC,OAAO,CAAA;AACpB;AACA,IAAI,MAAM,oCAAqC,GAAE,iBAAiB,aAAa,CAAC,uBAAuB,CAAA;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,0BAA0B,oCAAA,IAAwC,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAA;AAChH,IAAI,IAAI,WAAY,IAAG,IAAI,CAAC,8BAAA,IAAkC,oCAAoC,EAAE;AACpG,MAAM,MAAM,CAAC,IAAI;AACjB,QAAQ,wKAAwK;AAChL,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,iBAAiB;AACrB,MAAM,CAAC,OAAO,KAAyB;AACvC,QAAQ,MAAM,cAAc,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAA;AACjE;AACA,QAAQ,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,uBAAwB;AAC1D,UAAU,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,uBAAuB,CAAC,WAAW,EAAE,OAAO,EAAE,aAAa,CAAC,CAAA;AAChG;AACA,QAAQ,OAAO,WAAW,CAAA;AAC1B,OAAO;AACP,MAAM,0BAA0B;AAChC,MAAM,gCAAgC;AACtC,KAAK,CAAA;AACL;AACA,IAAI,IAAI,0BAA0B,EAAE;AACpC,MAAM,8BAA8B,EAAE,CAAA;AACtC,KAAI;AACJ;AACA,IAAI,IAAI,YAAY,CAAC,kBAAkB,EAAE;AACzC,MAAM,IAAI,CAAC,4BAA4B,EAAE,CAAA;AACzC,KAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAChC,MAAM,IAAI,CAAC,+BAA+B,EAAE,CAAA;AAC5C,KAAI;AACJ;AACA,IAAI,0BAA0B,CAAC;AAC/B,MAAM,UAAU;AAChB,MAAM,QAAQ;AACd,MAAM,uBAAuB;AAC7B,MAAM,0BAA0B;AAChC,MAAM,iBAAiB;AACvB,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA,GAAU,uBAAuB,CAAC,OAAO,EAA+C;AACxF,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAC9B,MAAM,WAAY;AAClB,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,EAAE,CAAC,+CAA+C,CAAC,CAAC,CAAA;AAC5G,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ;AACA,IAAI,MAAM,GAAI,GAAE,IAAI,CAAC,cAAc,EAAE,CAAA;AACrC;AACA,IAAI,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAkB,EAAA,GAAI,IAAI,CAAC,OAAO,CAAA;AACzF;AACA,IAAI,MAAM,qBAAsB,GAAE,OAAO,CAAC,EAAA,KAAO,UAAU,CAAA;AAC3D;AACA,IAAI,IAAI,eAAe,CAAA;AACvB,IAAI,IAAI,qBAAqB,EAAE;AAC/B,MAAM,MAAM,WAAY,GAAE,qBAAsB,GAAE,cAAc,CAAC,cAAc,CAAE,GAAE,EAAE,CAAA;AACrF,MAAM,MAAM,OAAQ,GAAE,qBAAsB,GAAE,cAAc,CAAC,SAAS,CAAE,GAAE,SAAS,CAAA;AACnF,MAAM,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,YAAY,EAAE,OAAQ,EAAA,GAAI,6BAA6B,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;AACzG,MAAM,kBAAkB;AACxB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,aAAa,EAAE,OAAO;AAC9B,QAAQ,GAAG,OAAO;AAClB,QAAQ,QAAQ,EAAE;AAClB;AACA,UAAU,GAAG,OAAO,CAAC,QAAQ;AAC7B,UAAU,sBAAsB,EAAE,GAAG;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,IAAI;AACrB,OAAO,CAAA;AACP,WAAW;AACX,MAAM,kBAAkB;AACxB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,GAAG,OAAO;AAClB,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,MAAM,eAAA,GAAkB,OAAO,cAAe,KAAI,UAAW,GAAE,cAAc,CAAC,eAAe,CAAA,GAAI,eAAe,CAAA;AACpH;AACA;AACA;AACA,IAAI,MAAM,YAAa,GAAE,eAAgB,KAAI,YAAY,EAAE,GAAG,eAAe,EAAE,OAAO,EAAE,KAAM,EAAA,GAAI,eAAe,CAAA;AACjH;AACA;AACA;AACA,IAAI,YAAY,CAAC,QAAS;AAC1B,MAAM,YAAY,CAAC,IAAK,KAAI,eAAe,CAAC,IAAA;AAC5C;AACA,UAAU,EAAE,GAAG,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAS,EAAA;AACvD;AACA,UAAU,YAAY,CAAC,QAAQ,CAAA;AAC/B;AACA,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,YAAY,CAAC,IAAI,CAAA;AAC9C,IAAI,IAAI,CAAC,YAAY,CAAC,OAAA,GAAU,YAAY,CAAA;AAC5C;AACA;AACA,IAAI,IAAI,YAAY,CAAC,OAAQ,KAAI,KAAK,EAAE;AACxC,MAAM,WAAY,IAAG,MAAM,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,YAAY,CAAC,EAAE,CAAC,uCAAuC,CAAC,CAAC,CAAA;AACpH,KAAI;AACJ;AACA,IAAI,WAAY,IAAG,MAAM,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,YAAY,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAC,CAAA;AAC3F;AACA,IAAI,MAAM,EAAE,QAAS,EAAA,GAAI,MAAM,CAAA;AAC/B;AACA,IAAI,MAAM,eAAgB,GAAE,oBAAoB;AAChD,MAAM,GAAG;AACT,MAAM,YAAY;AAClB,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,IAAI;AACV,MAAM,EAAE,UAAU;AAClB,MAAM,iBAAiB;AACvB,MAAM,qBAAqB;AAC3B,KAAK,CAAA;AACL;AACA,IAAI,IAAI,qBAAqB,EAAE;AAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC3B,QAAQ,MAAM,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;AACnE,UAAU,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAE,UAAU,CAAC,EAAE;AACjF,YAAY,eAAe,CAAC,oBAAoB,EAAE,CAAA;AAClD,WAAU;AACV,SAAS,CAAC,CAAA;AACV;AACA,QAAQ,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;AAC9E,UAAU,eAAe,CAAC,oBAAoB,EAAE,CAAA;AAChD,SAAQ;AACR,OAAM;AACN,KAAI;AACJ;AACA,IAAI,eAAe,CAAC,4BAA4B,CAAC,eAAe;AAChE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;AAC9B,MAAM,qBAAqB,CAAC,WAAW,CAAC,CAAA;AACxC,KAAK,CAAC,CAAA;AACN;AACA,IAAI,OAAO,eAAgB,EAAA;AAC3B,GAAE;AACF;AACA;AACA,GAAU,4BAA4B,GAAS;AAC/C,IAAI,IAAI,8BAA8B,CAAA;AACtC,IAAI,MAAM,8BAAA,GAAiC,MAAY;AACvD,MAAM,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAA,EAAoB,GAAE,IAAI,CAAC,OAAO,CAAA;AAC3E,MAAM,MAAM,EAAG,GAAE,iBAAiB,CAAA;AAClC;AACA;AACA,MAAM,MAAM,kBAAA,GAAqB,oBAAoB,EAAE,CAAA;AACvD,MAAM,IAAI,kBAAmB,IAAG,kBAAkB,CAAC,EAAA,IAAM,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC,EAAE;AACrH,QAAQ,WAAY;AACpB,UAAU,MAAM,CAAC,IAAI;AACrB,YAAY,CAAC,yBAAyB,EAAE,EAAE,CAAC,yEAAyE,CAAC;AACrH,WAAW,CAAA;AACX,QAAQ,OAAO,SAAS,CAAA;AACxB,OAAM;AACN;AACA,MAAM,IAAI,8BAA8B,EAAE;AAC1C,QAAQ,8BAA8B,CAAC,eAAe,CAAC,wBAAwB,CAAC,CAAA;AAChF,QAAQ,8BAA8B,CAAC,GAAG,EAAE,CAAA;AAC5C,QAAQ,8BAAA,GAAiC,SAAS,CAAA;AAClD,OAAM;AACN;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAChC,QAAQ,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,EAAE,CAAC,+CAA+C,CAAC,CAAC,CAAA;AACnH,QAAQ,OAAO,SAAS,CAAA;AACxB,OAAM;AACN;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACnC,QAAQ,WAAY,IAAG,MAAM,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,EAAE,CAAC,iDAAiD,CAAC,CAAC,CAAA;AACrH,QAAQ,OAAO,SAAS,CAAA;AACxB,OAAM;AACN;AACA,MAAM,MAAM,GAAI,GAAE,IAAI,CAAC,cAAc,EAAE,CAAA;AACvC,MAAM,MAAM,EAAE,QAAS,EAAA,GAAI,MAAM,CAAA;AACjC;AACA,MAAM,MAAM,OAAO,GAAuB;AAC1C,QAAQ,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;AACpC,QAAQ,EAAE;AACV,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,IAAI,EAAE;AACd,UAAU,CAAC,gCAAgC,GAAG,IAAI,CAAC,YAAY,CAAC,OAAA;AAChE,cAAc,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAA;AACjD,cAA2B,KAAK;AAChC,SAAS;AACT,OAAO,CAAA;AACP;AACA,MAAM,8BAAA,GAAiC,oBAAoB;AAC3D,QAAQ,GAAG;AACX,QAAQ,OAAO;AACf,QAAQ,WAAW;AACnB,QAAQ,YAAY;AACpB,QAAQ,IAAI;AACZ,QAAQ,EAAE,UAAU;AACpB,QAAQ,iBAAiB;AACzB,OAAO,CAAA;AACP,KAAK,CAAA;AACL;AACA,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ;AAC9B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC3B,QAAQ,gBAAgB,CAAC,IAAI,EAAE,8BAA8B,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAA,EAAM,CAAC,CAAA;AAC9F,OAAM;AACN,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA,GAAU,+BAA+B,GAAS;AAClD,IAAI,MAAM,aAAc,GAAE,CAAC,EAAE,OAAA,EAAS,KAA4C;AAClF,MAAM,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAChC;AACA;AACA,MAAM,MAAM,MAAO;AACnB,QAAQ,WAAW,SAAA,IAAa,MAAM,CAAC,yBAAyB,SAAA;AAChE,aAAa,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAE;AACnD,YAAY,SAAS,CAAA;AACrB,MAAM,MAAM,QAAA,GAAW,MAAA,KAAW,SAAA,GAAY,MAAM,CAAC,WAAW,EAAC,GAAI,SAAS,CAAA;AAC9E;AACA,MAAM,MAAM,iBAAA,GAAoB,oBAAoB,EAAE,CAAA;AACtD,MAAM,MAAM,YAAA,GAAe,eAAe,EAAE,CAAA;AAC5C,MAAM,MAAM,IAAA,GAAO,YAAA,KAAiB,SAAA,GAAY,YAAY,CAAC,OAAO,EAAC,GAAI,SAAS,CAAA;AAClF,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS;AAC/B,QAAQ,IAAI,wBAAwB,CAAC,KAAK,CAAC,EAAE;AAC7C,UAAU,MAAM,aAAA,GAAgB,KAAK,CAAC,aAAa,CAAA;AACnD,UAAU,IAAI,aAAc,KAAI,SAAS,EAAE;AAC3C,YAAY,OAAM;AAClB,WAAU;AACV,UAAU,MAAM,sBAAsB,IAAI,CAAC,gCAAgC,CAAC,aAAa,CAAC,CAAA;AAC1F,UAAU,MAAM,QAAA,GAAW,KAAK,CAAC,QAAQ,CAAA;AACzC,UAAU,MAAM,SAAA,GAAY,KAAK,CAAC,SAAS,CAAA;AAC3C,UAAU,MAAM,IAAK,GAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;AACzE,UAAU,MAAM,gBAAiB;AACjC,YAAY,IAAI,CAAC,MAAA,GAAS,CAAA;AAC1B,gBAAgB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AACtC,kBAAkB,OAAO,IAAI,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,QAAS;AAC3E,oBAAoB,IAAI,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,QAAA;AAC7D,sBAAsB,CAAA;AACtB,sBAAsB,CAAC,CAAA;AACvB,iBAAiB,CAAA;AACjB,gBAAgB,SAAS,CAAA;AACzB;AACA;AACA,UAAU,IAAI,KAAK,CAAC,SAAU,KAAI,aAAa,EAAE;AACjD,YAAY,MAAM,gBAAgB,IAAA;AAClC,eAAe,GAAG,CAAC,GAAI,IAAG,IAAI,CAAC,gCAAgC,CAAC,GAAG,CAAC,CAAA;AACpE,eAAe,IAAI,CAAC,WAAA,IAAe;AACnC,gBAAgB,OAAO,WAAW,CAAC,QAAS,KAAI,QAAS,IAAG,WAAW,CAAC,SAAU,KAAI,SAAS,CAAA;AAC/F,eAAe,CAAC,CAAA;AAChB,YAAY,IAAI,aAAa,EAAE;AAC/B,cAAc,OAAM;AACpB,aAAY;AACZ,WAAU;AACV;AACA,UAAU,IAAI,CAAC,aAAa,EAAE;AAC9B,YAAY,OAAM;AAClB,WAAU;AACV;AACA,UAAU,IAAI,mBAAmB,EAAE;AACnC,YAAY,mBAAmB,CAAC,QAAS,GAAE,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;AAC3F,WAAU,MAAO;AACjB,YAAY,IAAI,CAAC,MAAO,GAAE,gBAAiB;AAC3C,YAAY,gBAAA,KAAqB,SAAU;AAC3C,YAAY,QAAA,GAAW,IAAI,CAAC,gCAAgC,CAAC,gBAAgB,CAAC,CAAC,QAAA;AAC/E,YAAY;AACZ;AACA,YAAY,MAAM,SAAU,GAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAA;AACpD,YAAY,MAAM,aAAc,GAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAA;AAC3D,YAAY,IAAI,SAAU,IAAG,aAAa,EAAE;AAC5C,cAAc,IAAI,gBAAA,IAAoB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC,MAAO,IAAG,gBAAgB,EAAE;AACrH;AACA,gBAAgB,OAAO,IAAI,CAAC,gCAAgC,CAAC,gBAAgB,CAAC,CAAA;AAC9E,eAAc;AACd,cAAc,IAAI,CAAC,gCAAgC,CAAC,aAAa,IAAI;AACrE,gBAAgB,SAAS;AACzB,gBAAgB,QAAQ;AACxB,gBAAgB,aAAa;AAC7B,gBAAgB,IAAI;AACpB,gBAAgB,iBAAiB;AACjC,gBAAgB,QAAQ;AACxB,gBAAgB,SAAS;AACzB,eAAe,CAAA;AACf,aAAY;AACZ,WAAU;AACV,SAAQ;AACR,OAAO,CAAC,CAAA;AACR,KAAK,CAAA;AACL,IAAI,oCAAoC,CAAC,OAAO,EAAE,aAAa,CAAC,CAAA;AAChE,IAAI,oCAAoC,CAAC,aAAa,EAAE,aAAa,CAAC,CAAA;AACtE,GAAE;AACF,CAAA;AACA;AACA;AACO,SAAS,cAAc,CAAC,QAAQ,EAA8B;AACrE;AACA;AACA;AACA,EAAE,MAAM,OAAA,GAAU,aAAa,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;AACzD;AACA,EAAE,OAAO,OAAQ,GAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAE,GAAE,SAAS,CAAA;AAC9D,CAAA;AACA;AACA,SAAS,SAAS,CAAC,OAAO,EAAqD;AAC/E,EAAE,MAAM,oBAAA,GAAuB,OAAO,CAAC,UAAA,IAAc,OAAO,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAA;AACzG;AACA,EAAE,MAAM,cAAA,GAAiB,OAAO,CAAC,IAAA,IAAQ,OAAO,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;AACvF;AACA,EAAE,MAAM,kBAAmB,GAAE,OAAO,CAAC,QAAS,IAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAA;AACxE;AACA,EAAE,OAAO,oBAAA,IAAwB,cAAA,IAAkB,kBAAkB,CAAA;AACrE,CAAA;AACA;AACA,SAAS,wBAAwB,CAAC,KAAK,EAAqD;AAC5F,EAAE,OAAO,UAAW,IAAG,KAAK,CAAA;AAC5B;;;;"}