{"version": 3, "file": "fetch.js", "sources": ["../../../src/common/fetch.ts"], "sourcesContent": ["import {\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromClient,\n  getDynamicSamplingContextFromSpan,\n  getIsolationScope,\n  hasTracingEnabled,\n  setHttpStatus,\n  spanToTraceHeader,\n  startInactiveSpan,\n} from '@sentry/core';\nimport type { Client, HandlerDataFetch, Scope, Span, SpanOrigin } from '@sentry/types';\nimport {\n  BAGGAGE_HEADER_NAME,\n  dynamicSamplingContextToSentryBaggageHeader,\n  generateSentryTraceHeader,\n  isInstanceOf,\n  parseUrl,\n} from '@sentry/utils';\n\ntype PolymorphicRequestHeaders =\n  | Record<string, string | undefined>\n  | Array<[string, string]>\n  // the below is not preicsely the Header type used in Request, but it'll pass duck-typing\n  | {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      [key: string]: any;\n      append: (key: string, value: string) => void;\n      get: (key: string) => string | null | undefined;\n    };\n\n/**\n * Create and track fetch request spans for usage in combination with `addInstrumentationHandler`.\n *\n * @returns Span if a span was created, otherwise void.\n */\nexport function instrumentFetchRequest(\n  handlerData: HandlerDataFetch,\n  shouldCreateSpan: (url: string) => boolean,\n  shouldAttachHeaders: (url: string) => boolean,\n  spans: Record<string, Span>,\n  spanOrigin: SpanOrigin = 'auto.http.browser',\n): Span | undefined {\n  if (!hasTracingEnabled() || !handlerData.fetchData) {\n    return undefined;\n  }\n\n  const shouldCreateSpanResult = shouldCreateSpan(handlerData.fetchData.url);\n\n  if (handlerData.endTimestamp && shouldCreateSpanResult) {\n    const spanId = handlerData.fetchData.__span;\n    if (!spanId) return;\n\n    const span = spans[spanId];\n    if (span) {\n      endSpan(span, handlerData);\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[spanId];\n    }\n    return undefined;\n  }\n\n  const scope = getCurrentScope();\n  const client = getClient();\n\n  const { method, url } = handlerData.fetchData;\n\n  const fullUrl = getFullURL(url);\n  const host = fullUrl ? parseUrl(fullUrl).host : undefined;\n\n  const span = shouldCreateSpanResult\n    ? startInactiveSpan({\n        name: `${method} ${url}`,\n        onlyIfParent: true,\n        attributes: {\n          url,\n          type: 'fetch',\n          'http.method': method,\n          'http.url': fullUrl,\n          'server.address': host,\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: spanOrigin,\n        },\n        op: 'http.client',\n      })\n    : undefined;\n\n  if (span) {\n    handlerData.fetchData.__span = span.spanContext().spanId;\n    spans[span.spanContext().spanId] = span;\n  }\n\n  if (shouldAttachHeaders(handlerData.fetchData.url) && client) {\n    const request: string | Request = handlerData.args[0];\n\n    // In case the user hasn't set the second argument of a fetch call we default it to `{}`.\n    handlerData.args[1] = handlerData.args[1] || {};\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const options: { [key: string]: any } = handlerData.args[1];\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access\n    options.headers = addTracingHeadersToFetchRequest(request, client, scope, options, span);\n  }\n\n  return span;\n}\n\n/**\n * Adds sentry-trace and baggage headers to the various forms of fetch headers\n */\nexport function addTracingHeadersToFetchRequest(\n  request: string | unknown, // unknown is actually type Request but we can't export DOM types from this package,\n  client: Client,\n  scope: Scope,\n  options: {\n    headers?:\n      | {\n          [key: string]: string[] | string | undefined;\n        }\n      | PolymorphicRequestHeaders;\n  },\n  requestSpan?: Span,\n): PolymorphicRequestHeaders | undefined {\n  // eslint-disable-next-line deprecation/deprecation\n  const span = requestSpan || scope.getSpan();\n\n  const isolationScope = getIsolationScope();\n\n  const { traceId, spanId, sampled, dsc } = {\n    ...isolationScope.getPropagationContext(),\n    ...scope.getPropagationContext(),\n  };\n\n  const sentryTraceHeader = span ? spanToTraceHeader(span) : generateSentryTraceHeader(traceId, spanId, sampled);\n\n  const sentryBaggageHeader = dynamicSamplingContextToSentryBaggageHeader(\n    dsc ||\n      (span ? getDynamicSamplingContextFromSpan(span) : getDynamicSamplingContextFromClient(traceId, client, scope)),\n  );\n\n  const headers =\n    options.headers ||\n    (typeof Request !== 'undefined' && isInstanceOf(request, Request) ? (request as Request).headers : undefined);\n\n  if (!headers) {\n    return { 'sentry-trace': sentryTraceHeader, baggage: sentryBaggageHeader };\n  } else if (typeof Headers !== 'undefined' && isInstanceOf(headers, Headers)) {\n    const newHeaders = new Headers(headers as Headers);\n\n    newHeaders.append('sentry-trace', sentryTraceHeader);\n\n    if (sentryBaggageHeader) {\n      // If the same header is appended multiple times the browser will merge the values into a single request header.\n      // Its therefore safe to simply push a \"baggage\" entry, even though there might already be another baggage header.\n      newHeaders.append(BAGGAGE_HEADER_NAME, sentryBaggageHeader);\n    }\n\n    return newHeaders as PolymorphicRequestHeaders;\n  } else if (Array.isArray(headers)) {\n    const newHeaders = [...headers, ['sentry-trace', sentryTraceHeader]];\n\n    if (sentryBaggageHeader) {\n      // If there are multiple entries with the same key, the browser will merge the values into a single request header.\n      // Its therefore safe to simply push a \"baggage\" entry, even though there might already be another baggage header.\n      newHeaders.push([BAGGAGE_HEADER_NAME, sentryBaggageHeader]);\n    }\n\n    return newHeaders as PolymorphicRequestHeaders;\n  } else {\n    const existingBaggageHeader = 'baggage' in headers ? headers.baggage : undefined;\n    const newBaggageHeaders: string[] = [];\n\n    if (Array.isArray(existingBaggageHeader)) {\n      newBaggageHeaders.push(...existingBaggageHeader);\n    } else if (existingBaggageHeader) {\n      newBaggageHeaders.push(existingBaggageHeader);\n    }\n\n    if (sentryBaggageHeader) {\n      newBaggageHeaders.push(sentryBaggageHeader);\n    }\n\n    return {\n      ...(headers as Exclude<typeof headers, Headers>),\n      'sentry-trace': sentryTraceHeader,\n      baggage: newBaggageHeaders.length > 0 ? newBaggageHeaders.join(',') : undefined,\n    };\n  }\n}\n\nfunction getFullURL(url: string): string | undefined {\n  try {\n    const parsed = new URL(url);\n    return parsed.href;\n  } catch {\n    return undefined;\n  }\n}\n\nfunction endSpan(span: Span, handlerData: HandlerDataFetch): void {\n  if (handlerData.response) {\n    setHttpStatus(span, handlerData.response.status);\n\n    const contentLength =\n      handlerData.response && handlerData.response.headers && handlerData.response.headers.get('content-length');\n\n    if (contentLength) {\n      const contentLengthNum = parseInt(contentLength);\n      if (contentLengthNum > 0) {\n        span.setAttribute('http.response_content_length', contentLengthNum);\n      }\n    }\n  } else if (handlerData.error) {\n    span.setStatus('internal_error');\n  }\n  span.end();\n}\n"], "names": [], "mappings": ";;;AAgCA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB;AACtC,EAAE,WAAW;AACb,EAAE,gBAAgB;AAClB,EAAE,mBAAmB;AACrB,EAAE,KAAK;AACP,EAAE,UAAU,GAAe,mBAAmB;AAC9C,EAAoB;AACpB,EAAE,IAAI,CAAC,iBAAiB,EAAG,IAAG,CAAC,WAAW,CAAC,SAAS,EAAE;AACtD,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,sBAAuB,GAAE,gBAAgB,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;AAC5E;AACA,EAAE,IAAI,WAAW,CAAC,YAAa,IAAG,sBAAsB,EAAE;AAC1D,IAAI,MAAM,MAAO,GAAE,WAAW,CAAC,SAAS,CAAC,MAAM,CAAA;AAC/C,IAAI,IAAI,CAAC,MAAM,EAAE,OAAM;AACvB;AACA,IAAI,MAAM,IAAK,GAAE,KAAK,CAAC,MAAM,CAAC,CAAA;AAC9B,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAA;AAChC;AACA,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA;AAC1B,KAAI;AACJ,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE,CAAA;AACjC,EAAE,MAAM,MAAA,GAAS,SAAS,EAAE,CAAA;AAC5B;AACA,EAAE,MAAM,EAAE,MAAM,EAAE,KAAM,GAAE,WAAW,CAAC,SAAS,CAAA;AAC/C;AACA,EAAE,MAAM,OAAQ,GAAE,UAAU,CAAC,GAAG,CAAC,CAAA;AACjC,EAAE,MAAM,IAAA,GAAO,OAAA,GAAU,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAK,GAAE,SAAS,CAAA;AAC3D;AACA,EAAE,MAAM,OAAO,sBAAA;AACf,MAAM,iBAAiB,CAAC;AACxB,QAAQ,IAAI,EAAE,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,GAAA,CAAA,CAAA;AACA,QAAA,YAAA,EAAA,IAAA;AACA,QAAA,UAAA,EAAA;AACA,UAAA,GAAA;AACA,UAAA,IAAA,EAAA,OAAA;AACA,UAAA,aAAA,EAAA,MAAA;AACA,UAAA,UAAA,EAAA,OAAA;AACA,UAAA,gBAAA,EAAA,IAAA;AACA,UAAA,CAAA,gCAAA,GAAA,UAAA;AACA,SAAA;AACA,QAAA,EAAA,EAAA,aAAA;AACA,OAAA,CAAA;AACA,MAAA,SAAA,CAAA;AACA;AACA,EAAA,IAAA,IAAA,EAAA;AACA,IAAA,WAAA,CAAA,SAAA,CAAA,MAAA,GAAA,IAAA,CAAA,WAAA,EAAA,CAAA,MAAA,CAAA;AACA,IAAA,KAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,MAAA,CAAA,GAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,mBAAA,CAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,OAAA,GAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA,IAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA;AACA;AACA;AACA,IAAA,MAAA,OAAA,GAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA,IAAA,OAAA,CAAA,OAAA,GAAA,+BAAA,CAAA,OAAA,EAAA,MAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,IAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,+BAAA;AACA,EAAA,OAAA;AACA,EAAA,MAAA;AACA,EAAA,KAAA;AACA,EAAA,OAAA;;AAMA;AACA,EAAA,WAAA;AACA,EAAA;AACA;AACA,EAAA,MAAA,IAAA,GAAA,WAAA,IAAA,KAAA,CAAA,OAAA,EAAA,CAAA;AACA;AACA,EAAA,MAAA,cAAA,GAAA,iBAAA,EAAA,CAAA;AACA;AACA,EAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,OAAA,EAAA,GAAA,EAAA,GAAA;AACA,IAAA,GAAA,cAAA,CAAA,qBAAA,EAAA;AACA,IAAA,GAAA,KAAA,CAAA,qBAAA,EAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,MAAA,iBAAA,GAAA,IAAA,GAAA,iBAAA,CAAA,IAAA,CAAA,GAAA,yBAAA,CAAA,OAAA,EAAA,MAAA,EAAA,OAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,mBAAA,GAAA,2CAAA;AACA,IAAA,GAAA;AACA,OAAA,IAAA,GAAA,iCAAA,CAAA,IAAA,CAAA,GAAA,mCAAA,CAAA,OAAA,EAAA,MAAA,EAAA,KAAA,CAAA,CAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,MAAA,OAAA;AACA,IAAA,OAAA,CAAA,OAAA;AACA,KAAA,OAAA,OAAA,KAAA,WAAA,IAAA,YAAA,CAAA,OAAA,EAAA,OAAA,CAAA,GAAA,CAAA,OAAA,GAAA,OAAA,GAAA,SAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,OAAA,EAAA;AACA,IAAA,OAAA,EAAA,cAAA,EAAA,iBAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,CAAA;AACA,GAAA,MAAA,IAAA,OAAA,OAAA,KAAA,WAAA,IAAA,YAAA,CAAA,OAAA,EAAA,OAAA,CAAA,EAAA;AACA,IAAA,MAAA,UAAA,GAAA,IAAA,OAAA,CAAA,OAAA,EAAA,CAAA;AACA;AACA,IAAA,UAAA,CAAA,MAAA,CAAA,cAAA,EAAA,iBAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,mBAAA,EAAA;AACA;AACA;AACA,MAAA,UAAA,CAAA,MAAA,CAAA,mBAAA,EAAA,mBAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,OAAA,UAAA,EAAA;AACA,GAAA,MAAA,IAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,MAAA,UAAA,GAAA,CAAA,GAAA,OAAA,EAAA,CAAA,cAAA,EAAA,iBAAA,CAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,mBAAA,EAAA;AACA;AACA;AACA,MAAA,UAAA,CAAA,IAAA,CAAA,CAAA,mBAAA,EAAA,mBAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,OAAA,UAAA,EAAA;AACA,GAAA,MAAA;AACA,IAAA,MAAA,qBAAA,GAAA,SAAA,IAAA,OAAA,GAAA,OAAA,CAAA,OAAA,GAAA,SAAA,CAAA;AACA,IAAA,MAAA,iBAAA,GAAA,EAAA,CAAA;AACA;AACA,IAAA,IAAA,KAAA,CAAA,OAAA,CAAA,qBAAA,CAAA,EAAA;AACA,MAAA,iBAAA,CAAA,IAAA,CAAA,GAAA,qBAAA,CAAA,CAAA;AACA,KAAA,MAAA,IAAA,qBAAA,EAAA;AACA,MAAA,iBAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,mBAAA,EAAA;AACA,MAAA,iBAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,OAAA;AACA,MAAA,IAAA,OAAA,EAAA;AACA,MAAA,cAAA,EAAA,iBAAA;AACA,MAAA,OAAA,EAAA,iBAAA,CAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,CAAA,IAAA,CAAA,GAAA,CAAA,GAAA,SAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA,SAAA,UAAA,CAAA,GAAA,EAAA;AACA,EAAA,IAAA;AACA,IAAA,MAAA,MAAA,GAAA,IAAA,GAAA,CAAA,GAAA,CAAA,CAAA;AACA,IAAA,OAAA,MAAA,CAAA,IAAA,CAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,OAAA,SAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA,SAAA,OAAA,CAAA,IAAA,EAAA,WAAA,EAAA;AACA,EAAA,IAAA,WAAA,CAAA,QAAA,EAAA;AACA,IAAA,aAAA,CAAA,IAAA,EAAA,WAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;AACA;AACA,IAAA,MAAA,aAAA;AACA,MAAA,WAAA,CAAA,QAAA,IAAA,WAAA,CAAA,QAAA,CAAA,OAAA,IAAA,WAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,gBAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,aAAA,EAAA;AACA,MAAA,MAAA,gBAAA,GAAA,QAAA,CAAA,aAAA,CAAA,CAAA;AACA,MAAA,IAAA,gBAAA,GAAA,CAAA,EAAA;AACA,QAAA,IAAA,CAAA,YAAA,CAAA,8BAAA,EAAA,gBAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,MAAA,IAAA,WAAA,CAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,SAAA,CAAA,gBAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,CAAA,GAAA,EAAA,CAAA;AACA;;;;"}