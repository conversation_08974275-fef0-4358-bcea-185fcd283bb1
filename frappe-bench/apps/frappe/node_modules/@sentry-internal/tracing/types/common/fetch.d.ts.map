{"version": 3, "file": "fetch.d.ts", "sourceRoot": "", "sources": ["../../../src/common/fetch.ts"], "names": [], "mappings": "AAYA,OAAO,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AASvF,KAAK,yBAAyB,GAC1B,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,GAClC,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,GAEvB;IAEE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;IACnB,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAC7C,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACjD,CAAC;AAEN;;;;GAIG;AACH,wBAAgB,sBAAsB,CACpC,WAAW,EAAE,gBAAgB,EAC7B,gBAAgB,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,EAC1C,mBAAmB,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,EAC7C,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,EAC3B,UAAU,GAAE,UAAgC,GAC3C,IAAI,GAAG,SAAS,CA+DlB;AAED;;GAEG;AACH,wBAAgB,+BAA+B,CAC7C,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,oFAAoF;AAC/G,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE;IACP,OAAO,CAAC,EACJ;QACE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,MAAM,GAAG,SAAS,CAAC;KAC9C,GACD,yBAAyB,CAAC;CAC/B,EACD,WAAW,CAAC,EAAE,IAAI,GACjB,yBAAyB,GAAG,SAAS,CAkEvC"}