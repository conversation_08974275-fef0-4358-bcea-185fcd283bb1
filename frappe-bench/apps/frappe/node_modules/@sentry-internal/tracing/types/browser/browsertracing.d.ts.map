{"version": 3, "file": "browsertracing.d.ts", "sourceRoot": "", "sources": ["../../../src/browser/browsertracing.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,GAAG,EAAmB,MAAM,cAAc,CAAC;AASzD,OAAO,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,EAAqB,MAAM,eAAe,CAAC;AAarH,OAAO,KAAK,EAAE,6BAA6B,EAAE,MAAM,WAAW,CAAC;AAM/D,eAAO,MAAM,8BAA8B,mBAAmB,CAAC;AAE/D,8CAA8C;AAC9C,MAAM,WAAW,qBAAsB,SAAQ,6BAA6B;IAC1E;;;;;;;;;OASG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;;;;OAMG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;;;;;OAMG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;;;OAIG;IACH,gCAAgC,EAAE,OAAO,CAAC;IAE1C;;;;OAIG;IACH,0BAA0B,EAAE,OAAO,CAAC;IAEpC;;;;;;OAMG;IACH,0BAA0B,EAAE,OAAO,CAAC;IAEpC;;;;OAIG;IACH,cAAc,EAAE,OAAO,CAAC;IAExB;;;;OAIG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;;;;;OAMG;IACH,sBAAsB,EAAE,MAAM,CAAC;IAE/B;;;;;;OAMG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;QACvB;;WAEG;QACH,iBAAiB,EAAE,OAAO,CAAC;KAC5B,CAAC,CAAC;IAEH;;;;;;;OAOG;IACH,YAAY,EAAE,OAAO,CAAC;QACpB,cAAc,EAAE,OAAO,CAAC;QACxB,kBAAkB,EAAE,OAAO,CAAC;QAE5B,uBAAuB,EAAE,CAAC,CAAC,EAAE,WAAW,GAAG,SAAS,EAAE,GAAG,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,GAAG,KAAK,IAAI,CAAC;KAClH,CAAC,CAAC;IAEH;;;;;;;;;OASG;IACH,cAAc,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,kBAAkB,GAAG,kBAAkB,GAAG,SAAS,CAAC;IAEzF;;;OAGG;IACH,sBAAsB,CAAC,CAAC,SAAS,WAAW,EAC1C,IAAI,EAAE,IAAI,EACV,sBAAsB,EAAE,CAAC,OAAO,EAAE,kBAAkB,KAAK,CAAC,GAAG,SAAS,EACtE,0BAA0B,CAAC,EAAE,OAAO,EACpC,gCAAgC,CAAC,EAAE,OAAO,GACzC,IAAI,CAAC;CACT;AAkBD;;;;;;;;GAQG;AACH,qBAAa,cAAe,YAAW,WAAW;IAMhD,0CAA0C;IACnC,OAAO,EAAE,qBAAqB,CAAC;IAEtC;;OAEG;IACI,IAAI,EAAE,MAAM,CAAC;IAGpB,OAAO,CAAC,cAAc,CAAC,CAAY;IAEnC,OAAO,CAAC,iBAAiB,CAAa;IAEtC,OAAO,CAAC,8BAA8B,CAAU;IAChD,OAAO,CAAC,gCAAgC,CAA8B;IACtE,OAAO,CAAC,YAAY,CAGlB;gBAEiB,QAAQ,CAAC,EAAE,OAAO,CAAC,qBAAqB,CAAC;IAsD5D;;OAEG;IAEI,SAAS,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,cAAc,KAAK,IAAI,EAAE,aAAa,EAAE,MAAM,GAAG,GAAG,IAAI;IAwEvF,uCAAuC;IACvC,OAAO,CAAC,uBAAuB;IAiG/B,kDAAkD;IAClD,OAAO,CAAC,4BAA4B;IAgEpC,+GAA+G;IAC/G,OAAO,CAAC,+BAA+B;CAkFxC;AAED,sCAAsC;AACtC,wBAAgB,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAOnE"}