{"version": 3, "file": "browserTracingIntegration.d.ts", "sourceRoot": "", "sources": ["../../../src/browser/browserTracingIntegration.ts"], "names": [], "mappings": "AAYA,OAAO,KAAK,EACV,MAAM,EAGN,gBAAgB,EAIjB,MAAM,eAAe,CAAC;AACvB,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAmB1C,OAAO,KAAK,EAAE,6BAA6B,EAAE,MAAM,WAAW,CAAC;AAK/D,eAAO,MAAM,8BAA8B,mBAAmB,CAAC;AAE/D,8CAA8C;AAC9C,MAAM,WAAW,qBAAsB,SAAQ,6BAA6B;IAC1E;;;;;;;;;OASG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;;;;OAMG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;;;;;OAMG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;;;OAIG;IACH,kBAAkB,EAAE,OAAO,CAAC;IAE5B;;;;OAIG;IACH,oBAAoB,EAAE,OAAO,CAAC;IAE9B;;;;;;OAMG;IACH,kBAAkB,EAAE,OAAO,CAAC;IAE5B;;;;OAIG;IACH,cAAc,EAAE,OAAO,CAAC;IAExB;;;;OAIG;IACH,SAAS,EAAE,OAAO,CAAC;IAEnB;;;;;;OAMG;IACH,sBAAsB,EAAE,MAAM,CAAC;IAE/B;;;;;;OAMG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC;QACvB;;WAEG;QACH,iBAAiB,EAAE,OAAO,CAAC;KAC5B,CAAC,CAAC;IAEH;;;;;;;OAOG;IACH,YAAY,EAAE,OAAO,CAAC;QACpB,kBAAkB,EAAE,OAAO,CAAC;KAC7B,CAAC,CAAC;IAEH;;;OAGG;IACH,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,gBAAgB,CAAC;CACnE;AAcD;;;;;;;;GAQG;AACH,eAAO,MAAM,yBAAyB,cAAe,QAAQ,qBAAqB,CAAC;;;;;qBAxHpE,MAAM;sBASL,MAAM;2BASD,MAAM;4BAOL,OAAO;8BAOL,OAAO;4BAST,OAAO;wBAOX,OAAO;mBAOZ,OAAO;gCASM,MAAM;;YAU5B;;eAEG;+BACgB,OAAO;;sBAWd,QAAQ;YACpB,kBAAkB,EAAE,OAAO,CAAC;SAC7B,CAAC;qCAM0B,gBAAgB,KAAK,gBAAgB;;;;;;;;CA2RzC,CAAC;AAE3B;;;GAGG;AACH,wBAAgB,+BAA+B,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAU/G;AAED;;;GAGG;AACH,wBAAgB,iCAAiC,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,gBAAgB,GAAG,IAAI,GAAG,SAAS,CAUjH;AAED,sCAAsC;AACtC,wBAAgB,cAAc,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAOnE"}