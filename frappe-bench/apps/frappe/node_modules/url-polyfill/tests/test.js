(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./classes/Driver", "./classes/Async", "./classes/Tester"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    const Driver_1 = require("./classes/Driver");
    const Async_1 = require("./classes/Async");
    const Tester_1 = require("./classes/Tester");
    (function example() {
        const config = require('./config.json');
        const tester = new Tester_1.Tester(config.testServer);
        return tester.runForMany([
            Driver_1.Driver.EDGE,
            Driver_1.Driver.CHROME,
            // Driver.FIREFOX,
            // Driver.OPERA,
            Driver_1.Driver.IE
        ], async (driver) => {
            await driver.driver.manage().setTimeouts({
                pageLoad: 300000,
                script: 300000,
            });
            await driver.navigate(config.testHost);
            await Async_1.Async.$delay(500);
            await tester.test('Test URL', () => {
                return driver.executeScript(`
        var url = new URL('https://www.yahoo.com:80/?fr=yset_ie_syc_oracle&type=orcl_hpset#page0');

        if(url.hash !== '#page0') throw new Error('Invalid hash : ' + url.hash);
        if(url.host !== 'www.yahoo.com:80') throw new Error('Invalid host : ' + url.host);
        if(url.hostname !== 'www.yahoo.com') throw new Error('Invalid hostname : ' + url.hostname);
        if(url.href !== 'https://www.yahoo.com:80/?fr=yset_ie_syc_oracle&type=orcl_hpset#page0') throw new Error('Invalid href : ' + url.href);
        if(url.origin !== 'https://www.yahoo.com:80') throw new Error('Invalid origin : ' + url.origin);
        if(url.pathname !== '/') throw new Error('Invalid pathname : ' + url.pathname);
        if(url.port !== '80') throw new Error('Invalid port : ' + url.port);
        if(url.protocol !== 'https:') throw new Error('Invalid protocol : ' + url.protocol);
        if(url.search !== '?fr=yset_ie_syc_oracle&type=orcl_hpset') throw new Error('Invalid search : ' + url.search);

        url.searchParams.append('page', 1);
        if(url.search !== '?fr=yset_ie_syc_oracle&type=orcl_hpset&page=1') throw new Error('Invalid search (append page 1) : ' + url.search);

        url.searchParams.delete('type');
        if(url.search !== '?fr=yset_ie_syc_oracle&page=1') throw new Error('Invalid search (delete type) : ' + url.search);

        return url;
      `);
            });
            await tester.test('Test URLSearchParams', () => {
                return driver.executeScript(`
        var url = new URL('http://localhost/?a=b');

        if(url.searchParams !== url.searchParams) throw new Error('Expects url.searchParams === url.searchParams');
        
        url.search = 'c=b';
        if(url.searchParams.toString() !== 'c=b') throw new Error('Expects url.searchParams.toString() === c=b');
        
        url.searchParams.append('d', 'e');
        if(url.search !== '?c=b&d=e') throw new Error('Expects url.search === ?c=b&d=e');
        
        return url;
      `);
            });
            await tester.test('Test URLSearchParams special char encoding/decoding', () => {
                return driver.executeScript(`
        if(new URLSearchParams('a=2018-12-19T09:14:35%2B09:00').get('a') !== '2018-12-19T09:14:35+09:00') {
          throw new Error('a=2018-12-19T09:14:35%2B09:00 failed');
        }
        
        if(new URLSearchParams('a=one+two').get('a') !== 'one two') {
          throw new Error('a=one+two failed');
        }
      `);
            });
            await tester.test('Test URLSearchParams constructor', () => {
                return driver.executeScript(`
        var a = new URLSearchParams('b=1&a=2&c=3');
        if(a.toString() !== 'b=1&a=2&c=3') throw new Error('Invalid constructor with new URLSearchParams(\\'b=1&a=2&c=3\\')');
        
        var b = new URLSearchParams(a);
        if(b.toString() !== 'b=1&a=2&c=3') throw new Error('Invalid constructor with new URLSearchParams(new URLSearchParams(\\'b=1&a=2&c=3\\'))');
        
        var c = new URLSearchParams([['b', 1], ['a', 2], ['c', 3]]);
        if(c.toString() !== 'b=1&a=2&c=3') throw new Error('Invalid constructor with new URLSearchParams([[\\'b\\', 1], [\\'a\\', 2], [\\'c\\', 3]])');
        
        var d = new URLSearchParams({ 'b': 1, 'a': 2, 'c': 3 });
        if(d.toString() !== 'b=1&a=2&c=3') throw new Error('Invalid constructor with new URLSearchParams({ \\'b\\': 1, \\'a\\': 2, \\'c\\': 3 })');
        
      `);
            });
            await tester.test('Test URLSearchParams.sort', () => {
                return driver.executeScript(`
        var a = new URLSearchParams('b=1&a=2&c=3');
        a.sort();
        if(a.toString() !== 'a=2&b=1&c=3') throw new Error('Expects searchParams.sort().toString() === a=2&b=1&c=3');
      `);
            });
            await tester.test('Test URL with base', () => {
                return driver.executeScript(`
        var url = new URL('test', 'http://www.example.com/base');
        
        if(url.host !== 'www.example.com') throw new Error('Invalid host : ' + url.host);
        if(url.hostname !== 'www.example.com') throw new Error('Invalid hostname : ' + url.hostname);
        if(url.href !== 'http://www.example.com/test') throw new Error('Invalid href : ' + url.href);
        if(url.pathname !== '/test') throw new Error('Invalid pathname : ' + url.pathname);
        if(url.protocol !== 'http:') throw new Error('Invalid protocol : ' + url.protocol);
        if(url.search !== '') throw new Error('Invalid search : ' + url.search);
        
        return url;
      `);
            });
            await tester.test('Test URL with URL as base', () => {
                return driver.executeScript(`
        var base = new URL('http://www.example.com/base');
        var url = new URL('test', base);
        
        if(url.host !== 'www.example.com') throw new Error('Invalid host : ' + url.host);
        if(url.hostname !== 'www.example.com') throw new Error('Invalid hostname : ' + url.hostname);
        if(url.href !== 'http://www.example.com/test') throw new Error('Invalid href : ' + url.href);
        if(url.pathname !== '/test') throw new Error('Invalid pathname : ' + url.pathname);
        if(url.protocol !== 'http:') throw new Error('Invalid protocol : ' + url.protocol);
        if(url.search !== '') throw new Error('Invalid search : ' + url.search);
        
        return url;
      `);
            });
            await tester.test('Test pathname variations', () => {
                return driver.executeScript(`
        var url = new URL('test/long/path.html', 'http://www.example.com');
        if(url.pathname !== '/test/long/path.html') throw new Error('Invalid pathname : ' + url.pathname);
        url.pathname = 'a/b 1'
        if(url.pathname !== '/a/b%201') throw new Error('Invalid pathname : ' + url.pathname);
        return url;
      `);
            });
            await tester.test('Ensure url.href does\'nt finish with ? if url.search is empty', () => {
                return driver.executeScript(`
        var url = new URL('https://www.example.com/');
        url.searchParams.delete('foo');
        if(url.toString() !== 'https://www.example.com/') throw new Error('Invalid url : ' + url.toString());
      `);
            });
            await tester.test('URL SearchParams should have spaces encoded as "+"', () => {
                return driver.executeScript(`
        var url = new URL('https://www.example.com/');
        url.searchParams.set('foo', 'value with spaces');
        if(url.toString() !== 'https://www.example.com/?foo=value+with+spaces') throw new Error('Invalid url : ' + url.toString());

        var url = new URL('https://www.example.com/?foo=another+value+with+spaces');
        var fooParam = url.searchParams.get('foo');
        if(fooParam !== 'another value with spaces') throw new Error('Invalid "foo" param value : ' + fooParam);
      `);
            });
            await tester.test('Url Protocol should control the visibility of port in origin', () => {
                return driver.executeScript(`
        var url = new URL('https://www.example.com:443'); // No port for https on 443
        var url2 = new URL('http://www.example.com:8080'); // Port for http on 8080
        var url3 = new URL('https://www.example.com:80'); // port for https on 80
        
        if (url.origin !== 'https://www.example.com') throw new Error('Origin value is not correct ' + url.origin);
        if (url2.origin !== 'http://www.example.com:8080') throw new Error('Origin value is not correct ' + url2.origin);
        if (url3.origin !== 'https://www.example.com:80') throw new Error('Origin value is not correct ' + url3.origin);
      `);
            });
            await tester.test('URL constructor should throw on invalid URL', () => {
                return driver.executeScript(`
        try {
          var url = 'relative_url_without_base';
          new URL(url);
          throw new Error('Should have thrown for URL(' + url + ')');
        } catch (e) {
          if (!e instanceof TypeError) {
            throw new Error("Expected TypeError but got " + e);
          }
        }
      `);
            });
            await tester.test('URL constructor should not throw when using upper-cased base', () => {
                return driver.executeScript(`
        var url = new URL('/foo', 'https://EXAMPLE.COM');
        if (url.href !== 'https://example.com/foo') throw new Error('The HREF text-case is not correct')
      `);
            });
        });
    })().catch(_ => console.log('ERROR: ', _));
});
