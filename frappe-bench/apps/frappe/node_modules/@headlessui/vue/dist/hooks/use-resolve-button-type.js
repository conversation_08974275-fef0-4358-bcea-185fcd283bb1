import{ref as f,onMounted as i,watchEffect as l}from"vue";import{dom as u}from'../utils/dom.js';function r(t,e){if(t)return t;let n=e!=null?e:"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function b(t,e){let n=f(r(t.value.type,t.value.as));return i(()=>{n.value=r(t.value.type,t.value.as)}),l(()=>{var o;n.value||u(e)&&u(e)instanceof HTMLButtonElement&&!((o=u(e))!=null&&o.hasAttribute("type"))&&(n.value="button")}),n}export{b as useResolveButtonType};
