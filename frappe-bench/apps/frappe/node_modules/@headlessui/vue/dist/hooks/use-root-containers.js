import{ref as f,h as a}from"vue";import{Hidden as m,Features as d}from'../internal/hidden.js';import{getOwnerDocument as T}from'../utils/owner.js';import{dom as H}from'../utils/dom.js';function p({defaultContainers:t=[],portals:o,mainTreeNodeRef:s}={}){let i=f(null),r=T(i);function u(){var l;let n=[];for(let e of t)e!==null&&(e instanceof HTMLElement?n.push(e):"value"in e&&e.value instanceof HTMLElement&&n.push(e.value));if(o!=null&&o.value)for(let e of o.value)n.push(e);for(let e of(l=r==null?void 0:r.querySelectorAll("html > *, body > *"))!=null?l:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&e.id!=="headlessui-portal-root"&&(e.contains(H(i))||n.some(c=>e.contains(c))||n.push(e));return n}return{resolveContainers:u,contains(n){return u().some(l=>l.contains(n))},mainTreeNodeRef:i,MainTreeNode(){return s!=null?null:a(m,{features:d.Hidden,ref:i})}}}function N(){let t=f(null);return{mainTreeNodeRef:t,MainTreeNode(){return a(m,{features:d.Hidden,ref:t})}}}export{N as useMainTreeNode,p as useRootContainers};
