import{computed as E,ref as s}from"vue";import{FocusableMode as d,isFocusableElement as p}from'../utils/focus-management.js';import{dom as C}from'../utils/dom.js';import{useDocumentEvent as l}from'./use-document-event.js';import{useWindowEvent as T}from'./use-window-event.js';function y(f,c,i=E(()=>!0)){function a(e,r){if(!i.value||e.defaultPrevented)return;let t=r(e);if(t===null||!t.getRootNode().contains(t))return;let m=function o(n){return typeof n=="function"?o(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let o of m){if(o===null)continue;let n=o instanceof HTMLElement?o:C(o);if(n!=null&&n.contains(t)||e.composed&&e.composedPath().includes(n))return}return!p(t,d.Loose)&&t.tabIndex!==-1&&e.preventDefault(),c(e,t)}let u=s(null);l("pointerdown",e=>{var r,t;i.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),l("mousedown",e=>{var r,t;i.value&&(u.value=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),l("click",e=>{u.value&&(a(e,()=>u.value),u.value=null)},!0),l("touchend",e=>a(e,()=>e.target instanceof HTMLElement?e.target:null),!0),T("blur",e=>a(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}export{y as useOutsideClick};
