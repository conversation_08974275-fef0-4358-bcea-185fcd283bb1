import{h as i,ref as m,defineComponent as f}from"vue";import{Hidden as l,Features as F}from'./hidden.js';let d=f({props:{onFocus:{type:Function,required:!0}},setup(t){let n=m(!0);return()=>n.value?i(l,{as:"button",type:"button",features:F.Focusable,onFocus(o){o.preventDefault();let e,a=50;function r(){var u;if(a--<=0){e&&cancelAnimationFrame(e);return}if((u=t.onFocus)!=null&&u.call(t)){n.value=!1,cancelAnimationFrame(e);return}e=requestAnimationFrame(r)}e=requestAnimationFrame(r)}}):null}});export{d as FocusSentinel};
