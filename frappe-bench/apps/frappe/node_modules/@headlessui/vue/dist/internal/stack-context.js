import{inject as f,provide as m,onMounted as l,onUnmounted as c,watch as s}from"vue";let u=Symbol("StackContext");var p=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(p||{});function v(){return f(u,()=>{})}function S({type:o,enabled:r,element:e,onUpdate:i}){let a=v();function t(...n){i==null||i(...n),a(...n)}l(()=>{s(r,(n,d)=>{n?t(0,o,e):d===!0&&t(1,o,e)},{immediate:!0,flush:"sync"})}),c(()=>{r.value&&t(1,o,e)}),m(u,t)}export{p as StackMessage,v as useStackContext,S as useStackProvider};
