{"version": 3, "file": "breadcrumbs.js", "sources": ["../../../../src/integrations/breadcrumbs.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport { addBreadcrumb, convertIntegrationFnToClass, defineIntegration, getClient } from '@sentry/core';\nimport type {\n  Client,\n  Event as SentryEvent,\n  HandlerDataConsole,\n  HandlerDataDom,\n  HandlerDataFetch,\n  HandlerDataHistory,\n  HandlerDataXhr,\n  Integration,\n  IntegrationClass,\n  IntegrationFn,\n} from '@sentry/types';\nimport type {\n  Breadcrumb,\n  FetchBreadcrumbData,\n  FetchBreadcrumbHint,\n  XhrBreadcrumbData,\n  XhrBreadcrumbHint,\n} from '@sentry/types/build/types/breadcrumb';\nimport {\n  SENTRY_XHR_DATA_KEY,\n  addClickKeypressInstrumentationHandler,\n  addConsoleInstrumentationHandler,\n  addFetchInstrumentationHandler,\n  addHistoryInstrumentationHandler,\n  addXhrInstrumentationHandler,\n  getComponentName,\n  getEventDescription,\n  htmlTreeAsString,\n  logger,\n  parseUrl,\n  safeJoin,\n  severityLevelFromString,\n} from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport { WINDOW } from '../helpers';\n\ninterface BreadcrumbsOptions {\n  console: boolean;\n  dom:\n    | boolean\n    | {\n        serializeAttribute?: string | string[];\n        maxStringLength?: number;\n      };\n  fetch: boolean;\n  history: boolean;\n  sentry: boolean;\n  xhr: boolean;\n}\n\n/** maxStringLength gets capped to prevent 100 breadcrumbs exceeding 1MB event payload size */\nconst MAX_ALLOWED_STRING_LENGTH = 1024;\n\nconst INTEGRATION_NAME = 'Breadcrumbs';\n\nconst _breadcrumbsIntegration = ((options: Partial<BreadcrumbsOptions> = {}) => {\n  const _options = {\n    console: true,\n    dom: true,\n    fetch: true,\n    history: true,\n    sentry: true,\n    xhr: true,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    setup(client) {\n      if (_options.console) {\n        addConsoleInstrumentationHandler(_getConsoleBreadcrumbHandler(client));\n      }\n      if (_options.dom) {\n        addClickKeypressInstrumentationHandler(_getDomBreadcrumbHandler(client, _options.dom));\n      }\n      if (_options.xhr) {\n        addXhrInstrumentationHandler(_getXhrBreadcrumbHandler(client));\n      }\n      if (_options.fetch) {\n        addFetchInstrumentationHandler(_getFetchBreadcrumbHandler(client));\n      }\n      if (_options.history) {\n        addHistoryInstrumentationHandler(_getHistoryBreadcrumbHandler(client));\n      }\n      if (_options.sentry && client.on) {\n        client.on('beforeSendEvent', _getSentryBreadcrumbHandler(client));\n      }\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const breadcrumbsIntegration = defineIntegration(_breadcrumbsIntegration);\n\n/**\n * Default Breadcrumbs instrumentations\n *\n * @deprecated Use `breadcrumbsIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const Breadcrumbs = convertIntegrationFnToClass(INTEGRATION_NAME, breadcrumbsIntegration) as IntegrationClass<\n  Integration & { setup: (client: Client) => void }\n> & {\n  new (\n    options?: Partial<{\n      console: boolean;\n      dom:\n        | boolean\n        | {\n            serializeAttribute?: string | string[];\n            maxStringLength?: number;\n          };\n      fetch: boolean;\n      history: boolean;\n      sentry: boolean;\n      xhr: boolean;\n    }>,\n  ): Integration;\n};\n\n/**\n * Adds a breadcrumb for Sentry events or transactions if this option is enabled.\n */\nfunction _getSentryBreadcrumbHandler(client: Client): (event: SentryEvent) => void {\n  return function addSentryBreadcrumb(event: SentryEvent): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    addBreadcrumb(\n      {\n        category: `sentry.${event.type === 'transaction' ? 'transaction' : 'event'}`,\n        event_id: event.event_id,\n        level: event.level,\n        message: getEventDescription(event),\n      },\n      {\n        event,\n      },\n    );\n  };\n}\n\n/**\n * A HOC that creaes a function that creates breadcrumbs from DOM API calls.\n * This is a HOC so that we get access to dom options in the closure.\n */\nfunction _getDomBreadcrumbHandler(\n  client: Client,\n  dom: BreadcrumbsOptions['dom'],\n): (handlerData: HandlerDataDom) => void {\n  return function _innerDomBreadcrumb(handlerData: HandlerDataDom): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    let target;\n    let componentName;\n    let keyAttrs = typeof dom === 'object' ? dom.serializeAttribute : undefined;\n\n    let maxStringLength =\n      typeof dom === 'object' && typeof dom.maxStringLength === 'number' ? dom.maxStringLength : undefined;\n    if (maxStringLength && maxStringLength > MAX_ALLOWED_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `\\`dom.maxStringLength\\` cannot exceed ${MAX_ALLOWED_STRING_LENGTH}, but a value of ${maxStringLength} was configured. Sentry will use ${MAX_ALLOWED_STRING_LENGTH} instead.`,\n        );\n      maxStringLength = MAX_ALLOWED_STRING_LENGTH;\n    }\n\n    if (typeof keyAttrs === 'string') {\n      keyAttrs = [keyAttrs];\n    }\n\n    // Accessing event.target can throw (see getsentry/raven-js#838, #768)\n    try {\n      const event = handlerData.event as Event | Node;\n      const element = _isEvent(event) ? event.target : event;\n\n      target = htmlTreeAsString(element, { keyAttrs, maxStringLength });\n      componentName = getComponentName(element);\n    } catch (e) {\n      target = '<unknown>';\n    }\n\n    if (target.length === 0) {\n      return;\n    }\n\n    const breadcrumb: Breadcrumb = {\n      category: `ui.${handlerData.name}`,\n      message: target,\n    };\n\n    if (componentName) {\n      breadcrumb.data = { 'ui.component_name': componentName };\n    }\n\n    addBreadcrumb(breadcrumb, {\n      event: handlerData.event,\n      name: handlerData.name,\n      global: handlerData.global,\n    });\n  };\n}\n\n/**\n * Creates breadcrumbs from console API calls\n */\nfunction _getConsoleBreadcrumbHandler(client: Client): (handlerData: HandlerDataConsole) => void {\n  return function _consoleBreadcrumb(handlerData: HandlerDataConsole): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const breadcrumb = {\n      category: 'console',\n      data: {\n        arguments: handlerData.args,\n        logger: 'console',\n      },\n      level: severityLevelFromString(handlerData.level),\n      message: safeJoin(handlerData.args, ' '),\n    };\n\n    if (handlerData.level === 'assert') {\n      if (handlerData.args[0] === false) {\n        breadcrumb.message = `Assertion failed: ${safeJoin(handlerData.args.slice(1), ' ') || 'console.assert'}`;\n        breadcrumb.data.arguments = handlerData.args.slice(1);\n      } else {\n        // Don't capture a breadcrumb for passed assertions\n        return;\n      }\n    }\n\n    addBreadcrumb(breadcrumb, {\n      input: handlerData.args,\n      level: handlerData.level,\n    });\n  };\n}\n\n/**\n * Creates breadcrumbs from XHR API calls\n */\nfunction _getXhrBreadcrumbHandler(client: Client): (handlerData: HandlerDataXhr) => void {\n  return function _xhrBreadcrumb(handlerData: HandlerDataXhr): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const { startTimestamp, endTimestamp } = handlerData;\n\n    const sentryXhrData = handlerData.xhr[SENTRY_XHR_DATA_KEY];\n\n    // We only capture complete, non-sentry requests\n    if (!startTimestamp || !endTimestamp || !sentryXhrData) {\n      return;\n    }\n\n    const { method, url, status_code, body } = sentryXhrData;\n\n    const data: XhrBreadcrumbData = {\n      method,\n      url,\n      status_code,\n    };\n\n    const hint: XhrBreadcrumbHint = {\n      xhr: handlerData.xhr,\n      input: body,\n      startTimestamp,\n      endTimestamp,\n    };\n\n    addBreadcrumb(\n      {\n        category: 'xhr',\n        data,\n        type: 'http',\n      },\n      hint,\n    );\n  };\n}\n\n/**\n * Creates breadcrumbs from fetch API calls\n */\nfunction _getFetchBreadcrumbHandler(client: Client): (handlerData: HandlerDataFetch) => void {\n  return function _fetchBreadcrumb(handlerData: HandlerDataFetch): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    const { startTimestamp, endTimestamp } = handlerData;\n\n    // We only capture complete fetch requests\n    if (!endTimestamp) {\n      return;\n    }\n\n    if (handlerData.fetchData.url.match(/sentry_key/) && handlerData.fetchData.method === 'POST') {\n      // We will not create breadcrumbs for fetch requests that contain `sentry_key` (internal sentry requests)\n      return;\n    }\n\n    if (handlerData.error) {\n      const data: FetchBreadcrumbData = handlerData.fetchData;\n      const hint: FetchBreadcrumbHint = {\n        data: handlerData.error,\n        input: handlerData.args,\n        startTimestamp,\n        endTimestamp,\n      };\n\n      addBreadcrumb(\n        {\n          category: 'fetch',\n          data,\n          level: 'error',\n          type: 'http',\n        },\n        hint,\n      );\n    } else {\n      const response = handlerData.response as Response | undefined;\n      const data: FetchBreadcrumbData = {\n        ...handlerData.fetchData,\n        status_code: response && response.status,\n      };\n      const hint: FetchBreadcrumbHint = {\n        input: handlerData.args,\n        response,\n        startTimestamp,\n        endTimestamp,\n      };\n      addBreadcrumb(\n        {\n          category: 'fetch',\n          data,\n          type: 'http',\n        },\n        hint,\n      );\n    }\n  };\n}\n\n/**\n * Creates breadcrumbs from history API calls\n */\nfunction _getHistoryBreadcrumbHandler(client: Client): (handlerData: HandlerDataHistory) => void {\n  return function _historyBreadcrumb(handlerData: HandlerDataHistory): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    let from: string | undefined = handlerData.from;\n    let to: string | undefined = handlerData.to;\n    const parsedLoc = parseUrl(WINDOW.location.href);\n    let parsedFrom = from ? parseUrl(from) : undefined;\n    const parsedTo = parseUrl(to);\n\n    // Initial pushState doesn't provide `from` information\n    if (!parsedFrom || !parsedFrom.path) {\n      parsedFrom = parsedLoc;\n    }\n\n    // Use only the path component of the URL if the URL matches the current\n    // document (almost all the time when using pushState)\n    if (parsedLoc.protocol === parsedTo.protocol && parsedLoc.host === parsedTo.host) {\n      to = parsedTo.relative;\n    }\n    if (parsedLoc.protocol === parsedFrom.protocol && parsedLoc.host === parsedFrom.host) {\n      from = parsedFrom.relative;\n    }\n\n    addBreadcrumb({\n      category: 'navigation',\n      data: {\n        from,\n        to,\n      },\n    });\n  };\n}\n\nfunction _isEvent(event: unknown): event is Event {\n  return !!event && !!(event as Record<string, unknown>).target;\n}\n"], "names": ["addConsoleInstrumentationHandler", "addClickKeypressInstrumentationHandler", "addXhrInstrumentationHandler", "addFetchInstrumentationHandler", "addHistoryInstrumentationHandler", "defineIntegration", "convertIntegrationFnToClass", "getClient", "addBreadcrumb", "getEventDescription", "DEBUG_BUILD", "logger", "htmlTreeAsString", "getComponentName", "severityLevelFromString", "safeJoin", "SENTRY_XHR_DATA_KEY", "parseUrl", "WINDOW"], "mappings": ";;;;;;;AAAA;;AAsDA;AACA,MAAM,yBAAA,GAA4B,IAAI,CAAA;AACtC;AACA,MAAM,gBAAA,GAAmB,aAAa,CAAA;AACtC;AACA,MAAM,uBAAA,IAA2B,CAAC,OAAO,GAAgC,EAAE,KAAK;AAChF,EAAE,MAAM,WAAW;AACnB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,GAAG,EAAE,IAAI;AACb,IAAI,KAAK,EAAE,IAAI;AACf,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,GAAG,EAAE,IAAI;AACb,IAAI,GAAG,OAAO;AACd,GAAG,CAAA;AACH;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC5B,QAAQA,sCAAgC,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC,CAAA;AAC9E,OAAM;AACN,MAAM,IAAI,QAAQ,CAAC,GAAG,EAAE;AACxB,QAAQC,4CAAsC,CAAC,wBAAwB,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA;AAC9F,OAAM;AACN,MAAM,IAAI,QAAQ,CAAC,GAAG,EAAE;AACxB,QAAQC,kCAA4B,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,CAAA;AACtE,OAAM;AACN,MAAM,IAAI,QAAQ,CAAC,KAAK,EAAE;AAC1B,QAAQC,oCAA8B,CAAC,0BAA0B,CAAC,MAAM,CAAC,CAAC,CAAA;AAC1E,OAAM;AACN,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC5B,QAAQC,sCAAgC,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC,CAAA;AAC9E,OAAM;AACN,MAAM,IAAI,QAAQ,CAAC,UAAU,MAAM,CAAC,EAAE,EAAE;AACxC,QAAQ,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,2BAA2B,CAAC,MAAM,CAAC,CAAC,CAAA;AACzE,OAAM;AACN,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,sBAAuB,GAAEC,sBAAiB,CAAC,uBAAuB,EAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAcC,gCAA2B,CAAC,gBAAgB,EAAE,sBAAsB,CAAE;;CAkBjG;AACA;AACA;AACA;AACA;AACA,SAAS,2BAA2B,CAAC,MAAM,EAAwC;AACnF,EAAE,OAAO,SAAS,mBAAmB,CAAC,KAAK,EAAqB;AAChE,IAAI,IAAIC,cAAS,EAAG,KAAI,MAAM,EAAE;AAChC,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAIC,kBAAa;AACjB,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,IAAK,KAAI,gBAAgB,aAAA,GAAgB,OAAO,CAAC,CAAA;AACA,QAAA,QAAA,EAAA,KAAA,CAAA,QAAA;AACA,QAAA,KAAA,EAAA,KAAA,CAAA,KAAA;AACA,QAAA,OAAA,EAAAC,yBAAA,CAAA,KAAA,CAAA;AACA,OAAA;AACA,MAAA;AACA,QAAA,KAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,wBAAA;AACA,EAAA,MAAA;AACA,EAAA,GAAA;AACA,EAAA;AACA,EAAA,OAAA,SAAA,mBAAA,CAAA,WAAA,EAAA;AACA,IAAA,IAAAF,cAAA,EAAA,KAAA,MAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,MAAA,CAAA;AACA,IAAA,IAAA,aAAA,CAAA;AACA,IAAA,IAAA,QAAA,GAAA,OAAA,GAAA,KAAA,QAAA,GAAA,GAAA,CAAA,kBAAA,GAAA,SAAA,CAAA;AACA;AACA,IAAA,IAAA,eAAA;AACA,MAAA,OAAA,GAAA,KAAA,QAAA,IAAA,OAAA,GAAA,CAAA,eAAA,KAAA,QAAA,GAAA,GAAA,CAAA,eAAA,GAAA,SAAA,CAAA;AACA,IAAA,IAAA,eAAA,IAAA,eAAA,GAAA,yBAAA,EAAA;AACA,MAAAG,sBAAA;AACA,QAAAC,YAAA,CAAA,IAAA;AACA,UAAA,CAAA,sCAAA,EAAA,yBAAA,CAAA,iBAAA,EAAA,eAAA,CAAA,iCAAA,EAAA,yBAAA,CAAA,SAAA,CAAA;AACA,SAAA,CAAA;AACA,MAAA,eAAA,GAAA,yBAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,OAAA,QAAA,KAAA,QAAA,EAAA;AACA,MAAA,QAAA,GAAA,CAAA,QAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA;AACA,IAAA,IAAA;AACA,MAAA,MAAA,KAAA,GAAA,WAAA,CAAA,KAAA,EAAA;AACA,MAAA,MAAA,OAAA,GAAA,QAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,MAAA,GAAA,KAAA,CAAA;AACA;AACA,MAAA,MAAA,GAAAC,sBAAA,CAAA,OAAA,EAAA,EAAA,QAAA,EAAA,eAAA,EAAA,CAAA,CAAA;AACA,MAAA,aAAA,GAAAC,sBAAA,CAAA,OAAA,CAAA,CAAA;AACA,KAAA,CAAA,OAAA,CAAA,EAAA;AACA,MAAA,MAAA,GAAA,WAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,MAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,UAAA,GAAA;AACA,MAAA,QAAA,EAAA,CAAA,GAAA,EAAA,WAAA,CAAA,IAAA,CAAA,CAAA;AACA,MAAA,OAAA,EAAA,MAAA;AACA,KAAA,CAAA;AACA;AACA,IAAA,IAAA,aAAA,EAAA;AACA,MAAA,UAAA,CAAA,IAAA,GAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,CAAA;AACA,KAAA;AACA;AACA,IAAAL,kBAAA,CAAA,UAAA,EAAA;AACA,MAAA,KAAA,EAAA,WAAA,CAAA,KAAA;AACA,MAAA,IAAA,EAAA,WAAA,CAAA,IAAA;AACA,MAAA,MAAA,EAAA,WAAA,CAAA,MAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,4BAAA,CAAA,MAAA,EAAA;AACA,EAAA,OAAA,SAAA,kBAAA,CAAA,WAAA,EAAA;AACA,IAAA,IAAAD,cAAA,EAAA,KAAA,MAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,UAAA,GAAA;AACA,MAAA,QAAA,EAAA,SAAA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,SAAA,EAAA,WAAA,CAAA,IAAA;AACA,QAAA,MAAA,EAAA,SAAA;AACA,OAAA;AACA,MAAA,KAAA,EAAAO,6BAAA,CAAA,WAAA,CAAA,KAAA,CAAA;AACA,MAAA,OAAA,EAAAC,cAAA,CAAA,WAAA,CAAA,IAAA,EAAA,GAAA,CAAA;AACA,KAAA,CAAA;AACA;AACA,IAAA,IAAA,WAAA,CAAA,KAAA,KAAA,QAAA,EAAA;AACA,MAAA,IAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,KAAA,KAAA,EAAA;AACA,QAAA,UAAA,CAAA,OAAA,GAAA,CAAA,kBAAA,EAAAA,cAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,IAAA,gBAAA,CAAA,CAAA,CAAA;AACA,QAAA,UAAA,CAAA,IAAA,CAAA,SAAA,GAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA,MAAA;AACA;AACA,QAAA,OAAA;AACA,OAAA;AACA,KAAA;AACA;AACA,IAAAP,kBAAA,CAAA,UAAA,EAAA;AACA,MAAA,KAAA,EAAA,WAAA,CAAA,IAAA;AACA,MAAA,KAAA,EAAA,WAAA,CAAA,KAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,wBAAA,CAAA,MAAA,EAAA;AACA,EAAA,OAAA,SAAA,cAAA,CAAA,WAAA,EAAA;AACA,IAAA,IAAAD,cAAA,EAAA,KAAA,MAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,EAAA,cAAA,EAAA,YAAA,EAAA,GAAA,WAAA,CAAA;AACA;AACA,IAAA,MAAA,aAAA,GAAA,WAAA,CAAA,GAAA,CAAAS,yBAAA,CAAA,CAAA;AACA;AACA;AACA,IAAA,IAAA,CAAA,cAAA,IAAA,CAAA,YAAA,IAAA,CAAA,aAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,EAAA,MAAA,EAAA,GAAA,EAAA,WAAA,EAAA,IAAA,EAAA,GAAA,aAAA,CAAA;AACA;AACA,IAAA,MAAA,IAAA,GAAA;AACA,MAAA,MAAA;AACA,MAAA,GAAA;AACA,MAAA,WAAA;AACA,KAAA,CAAA;AACA;AACA,IAAA,MAAA,IAAA,GAAA;AACA,MAAA,GAAA,EAAA,WAAA,CAAA,GAAA;AACA,MAAA,KAAA,EAAA,IAAA;AACA,MAAA,cAAA;AACA,MAAA,YAAA;AACA,KAAA,CAAA;AACA;AACA,IAAAR,kBAAA;AACA,MAAA;AACA,QAAA,QAAA,EAAA,KAAA;AACA,QAAA,IAAA;AACA,QAAA,IAAA,EAAA,MAAA;AACA,OAAA;AACA,MAAA,IAAA;AACA,KAAA,CAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,0BAAA,CAAA,MAAA,EAAA;AACA,EAAA,OAAA,SAAA,gBAAA,CAAA,WAAA,EAAA;AACA,IAAA,IAAAD,cAAA,EAAA,KAAA,MAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,EAAA,cAAA,EAAA,YAAA,EAAA,GAAA,WAAA,CAAA;AACA;AACA;AACA,IAAA,IAAA,CAAA,YAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,WAAA,CAAA,SAAA,CAAA,GAAA,CAAA,KAAA,CAAA,YAAA,CAAA,IAAA,WAAA,CAAA,SAAA,CAAA,MAAA,KAAA,MAAA,EAAA;AACA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,WAAA,CAAA,KAAA,EAAA;AACA,MAAA,MAAA,IAAA,GAAA,WAAA,CAAA,SAAA,CAAA;AACA,MAAA,MAAA,IAAA,GAAA;AACA,QAAA,IAAA,EAAA,WAAA,CAAA,KAAA;AACA,QAAA,KAAA,EAAA,WAAA,CAAA,IAAA;AACA,QAAA,cAAA;AACA,QAAA,YAAA;AACA,OAAA,CAAA;AACA;AACA,MAAAC,kBAAA;AACA,QAAA;AACA,UAAA,QAAA,EAAA,OAAA;AACA,UAAA,IAAA;AACA,UAAA,KAAA,EAAA,OAAA;AACA,UAAA,IAAA,EAAA,MAAA;AACA,SAAA;AACA,QAAA,IAAA;AACA,OAAA,CAAA;AACA,KAAA,MAAA;AACA,MAAA,MAAA,QAAA,GAAA,WAAA,CAAA,QAAA,EAAA;AACA,MAAA,MAAA,IAAA,GAAA;AACA,QAAA,GAAA,WAAA,CAAA,SAAA;AACA,QAAA,WAAA,EAAA,QAAA,IAAA,QAAA,CAAA,MAAA;AACA,OAAA,CAAA;AACA,MAAA,MAAA,IAAA,GAAA;AACA,QAAA,KAAA,EAAA,WAAA,CAAA,IAAA;AACA,QAAA,QAAA;AACA,QAAA,cAAA;AACA,QAAA,YAAA;AACA,OAAA,CAAA;AACA,MAAAA,kBAAA;AACA,QAAA;AACA,UAAA,QAAA,EAAA,OAAA;AACA,UAAA,IAAA;AACA,UAAA,IAAA,EAAA,MAAA;AACA,SAAA;AACA,QAAA,IAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,4BAAA,CAAA,MAAA,EAAA;AACA,EAAA,OAAA,SAAA,kBAAA,CAAA,WAAA,EAAA;AACA,IAAA,IAAAD,cAAA,EAAA,KAAA,MAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,IAAA,GAAA,WAAA,CAAA,IAAA,CAAA;AACA,IAAA,IAAA,EAAA,GAAA,WAAA,CAAA,EAAA,CAAA;AACA,IAAA,MAAA,SAAA,GAAAU,cAAA,CAAAC,cAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AACA,IAAA,IAAA,UAAA,GAAA,IAAA,GAAAD,cAAA,CAAA,IAAA,CAAA,GAAA,SAAA,CAAA;AACA,IAAA,MAAA,QAAA,GAAAA,cAAA,CAAA,EAAA,CAAA,CAAA;AACA;AACA;AACA,IAAA,IAAA,CAAA,UAAA,IAAA,CAAA,UAAA,CAAA,IAAA,EAAA;AACA,MAAA,UAAA,GAAA,SAAA,CAAA;AACA,KAAA;AACA;AACA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,QAAA,KAAA,QAAA,CAAA,QAAA,IAAA,SAAA,CAAA,IAAA,KAAA,QAAA,CAAA,IAAA,EAAA;AACA,MAAA,EAAA,GAAA,QAAA,CAAA,QAAA,CAAA;AACA,KAAA;AACA,IAAA,IAAA,SAAA,CAAA,QAAA,KAAA,UAAA,CAAA,QAAA,IAAA,SAAA,CAAA,IAAA,KAAA,UAAA,CAAA,IAAA,EAAA;AACA,MAAA,IAAA,GAAA,UAAA,CAAA,QAAA,CAAA;AACA,KAAA;AACA;AACA,IAAAT,kBAAA,CAAA;AACA,MAAA,QAAA,EAAA,YAAA;AACA,MAAA,IAAA,EAAA;AACA,QAAA,IAAA;AACA,QAAA,EAAA;AACA,OAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,QAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,CAAA,CAAA,KAAA,IAAA,CAAA,CAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AACA;;;;;"}