{"version": 3, "file": "globalhandlers.js", "sources": ["../../../../src/integrations/globalhandlers.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unsafe-member-access */\nimport { captureEvent, convertIntegrationFnToClass, defineIntegration, getClient } from '@sentry/core';\nimport type {\n  Client,\n  Event,\n  Integration,\n  IntegrationClass,\n  IntegrationFn,\n  Primitive,\n  StackParser,\n} from '@sentry/types';\nimport {\n  addGlobalErrorInstrumentationHandler,\n  addGlobalUnhandledRejectionInstrumentationHandler,\n  getLocationHref,\n  isErrorEvent,\n  isPrimitive,\n  isString,\n  logger,\n} from '@sentry/utils';\n\nimport type { BrowserClient } from '../client';\nimport { DEBUG_BUILD } from '../debug-build';\nimport { eventFromUnknownInput } from '../eventbuilder';\nimport { shouldIgnoreOnError } from '../helpers';\n\ntype GlobalHandlersIntegrationsOptionKeys = 'onerror' | 'onunhandledrejection';\n\ntype GlobalHandlersIntegrations = Record<GlobalHandlersIntegrationsOptionKeys, boolean>;\n\nconst INTEGRATION_NAME = 'GlobalHandlers';\n\nconst _globalHandlersIntegration = ((options: Partial<GlobalHandlersIntegrations> = {}) => {\n  const _options = {\n    onerror: true,\n    onunhandledrejection: true,\n    ...options,\n  };\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      Error.stackTraceLimit = 50;\n    },\n    setup(client) {\n      if (_options.onerror) {\n        _installGlobalOnErrorHandler(client);\n        globalHandlerLog('onerror');\n      }\n      if (_options.onunhandledrejection) {\n        _installGlobalOnUnhandledRejectionHandler(client);\n        globalHandlerLog('onunhandledrejection');\n      }\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const globalHandlersIntegration = defineIntegration(_globalHandlersIntegration);\n\n/**\n * Global handlers.\n * @deprecated Use `globalHandlersIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const GlobalHandlers = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  globalHandlersIntegration,\n) as IntegrationClass<Integration & { setup: (client: Client) => void }> & {\n  new (options?: Partial<GlobalHandlersIntegrations>): Integration;\n};\n\nfunction _installGlobalOnErrorHandler(client: Client): void {\n  addGlobalErrorInstrumentationHandler(data => {\n    const { stackParser, attachStacktrace } = getOptions();\n\n    if (getClient() !== client || shouldIgnoreOnError()) {\n      return;\n    }\n\n    const { msg, url, line, column, error } = data;\n\n    const event =\n      error === undefined && isString(msg)\n        ? _eventFromIncompleteOnError(msg, url, line, column)\n        : _enhanceEventWithInitialFrame(\n            eventFromUnknownInput(stackParser, error || msg, undefined, attachStacktrace, false),\n            url,\n            line,\n            column,\n          );\n\n    event.level = 'error';\n\n    captureEvent(event, {\n      originalException: error,\n      mechanism: {\n        handled: false,\n        type: 'onerror',\n      },\n    });\n  });\n}\n\nfunction _installGlobalOnUnhandledRejectionHandler(client: Client): void {\n  addGlobalUnhandledRejectionInstrumentationHandler(e => {\n    const { stackParser, attachStacktrace } = getOptions();\n\n    if (getClient() !== client || shouldIgnoreOnError()) {\n      return;\n    }\n\n    const error = _getUnhandledRejectionError(e as unknown);\n\n    const event = isPrimitive(error)\n      ? _eventFromRejectionWithPrimitive(error)\n      : eventFromUnknownInput(stackParser, error, undefined, attachStacktrace, true);\n\n    event.level = 'error';\n\n    captureEvent(event, {\n      originalException: error,\n      mechanism: {\n        handled: false,\n        type: 'onunhandledrejection',\n      },\n    });\n  });\n}\n\nfunction _getUnhandledRejectionError(error: unknown): unknown {\n  if (isPrimitive(error)) {\n    return error;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const e = error as any;\n\n  // dig the object of the rejection out of known event types\n  try {\n    // PromiseRejectionEvents store the object of the rejection under 'reason'\n    // see https://developer.mozilla.org/en-US/docs/Web/API/PromiseRejectionEvent\n    if ('reason' in e) {\n      return e.reason;\n    }\n\n    // something, somewhere, (likely a browser extension) effectively casts PromiseRejectionEvents\n    // to CustomEvents, moving the `promise` and `reason` attributes of the PRE into\n    // the CustomEvent's `detail` attribute, since they're not part of CustomEvent's spec\n    // see https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent and\n    // https://github.com/getsentry/sentry-javascript/issues/2380\n    else if ('detail' in e && 'reason' in e.detail) {\n      return e.detail.reason;\n    }\n  } catch {} // eslint-disable-line no-empty\n\n  return error;\n}\n\n/**\n * Create an event from a promise rejection where the `reason` is a primitive.\n *\n * @param reason: The `reason` property of the promise rejection\n * @returns An Event object with an appropriate `exception` value\n */\nfunction _eventFromRejectionWithPrimitive(reason: Primitive): Event {\n  return {\n    exception: {\n      values: [\n        {\n          type: 'UnhandledRejection',\n          // String() is needed because the Primitive type includes symbols (which can't be automatically stringified)\n          value: `Non-Error promise rejection captured with value: ${String(reason)}`,\n        },\n      ],\n    },\n  };\n}\n\n/**\n * This function creates a stack from an old, error-less onerror handler.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction _eventFromIncompleteOnError(msg: any, url: any, line: any, column: any): Event {\n  const ERROR_TYPES_RE =\n    /^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;\n\n  // If 'message' is ErrorEvent, get real message from inside\n  let message = isErrorEvent(msg) ? msg.message : msg;\n  let name = 'Error';\n\n  const groups = message.match(ERROR_TYPES_RE);\n  if (groups) {\n    name = groups[1];\n    message = groups[2];\n  }\n\n  const event = {\n    exception: {\n      values: [\n        {\n          type: name,\n          value: message,\n        },\n      ],\n    },\n  };\n\n  return _enhanceEventWithInitialFrame(event, url, line, column);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction _enhanceEventWithInitialFrame(event: Event, url: any, line: any, column: any): Event {\n  // event.exception\n  const e = (event.exception = event.exception || {});\n  // event.exception.values\n  const ev = (e.values = e.values || []);\n  // event.exception.values[0]\n  const ev0 = (ev[0] = ev[0] || {});\n  // event.exception.values[0].stacktrace\n  const ev0s = (ev0.stacktrace = ev0.stacktrace || {});\n  // event.exception.values[0].stacktrace.frames\n  const ev0sf = (ev0s.frames = ev0s.frames || []);\n\n  const colno = isNaN(parseInt(column, 10)) ? undefined : column;\n  const lineno = isNaN(parseInt(line, 10)) ? undefined : line;\n  const filename = isString(url) && url.length > 0 ? url : getLocationHref();\n\n  // event.exception.values[0].stacktrace.frames\n  if (ev0sf.length === 0) {\n    ev0sf.push({\n      colno,\n      filename,\n      function: '?',\n      in_app: true,\n      lineno,\n    });\n  }\n\n  return event;\n}\n\nfunction globalHandlerLog(type: string): void {\n  DEBUG_BUILD && logger.log(`Global Handler attached: ${type}`);\n}\n\nfunction getOptions(): { stackParser: StackParser; attachStacktrace?: boolean } {\n  const client = getClient<BrowserClient>();\n  const options = (client && client.getOptions()) || {\n    stackParser: () => [],\n    attachStacktrace: false,\n  };\n  return options;\n}\n"], "names": ["defineIntegration", "convertIntegrationFnToClass", "addGlobalErrorInstrumentationHandler", "getClient", "shouldIgnoreOnError", "isString", "eventFromUnknownInput", "captureEvent", "addGlobalUnhandledRejectionInstrumentationHandler", "isPrimitive", "isErrorEvent", "getLocationHref", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;;;AAAA;;AA8BA,MAAM,gBAAA,GAAmB,gBAAgB,CAAA;AACzC;AACA,MAAM,0BAAA,IAA8B,CAAC,OAAO,GAAwC,EAAE,KAAK;AAC3F,EAAE,MAAM,WAAW;AACnB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,oBAAoB,EAAE,IAAI;AAC9B,IAAI,GAAG,OAAO;AACd,GAAG,CAAA;AACH;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,SAAS,GAAG;AAChB,MAAM,KAAK,CAAC,eAAgB,GAAE,EAAE,CAAA;AAChC,KAAK;AACL,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC5B,QAAQ,4BAA4B,CAAC,MAAM,CAAC,CAAA;AAC5C,QAAQ,gBAAgB,CAAC,SAAS,CAAC,CAAA;AACnC,OAAM;AACN,MAAM,IAAI,QAAQ,CAAC,oBAAoB,EAAE;AACzC,QAAQ,yCAAyC,CAAC,MAAM,CAAC,CAAA;AACzD,QAAQ,gBAAgB,CAAC,sBAAsB,CAAC,CAAA;AAChD,OAAM;AACN,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,yBAA0B,GAAEA,sBAAiB,CAAC,0BAA0B,EAAC;AACtF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAe,GAAEC,gCAA2B;AACzD,EAAE,gBAAgB;AAClB,EAAE,yBAAyB;AAC3B,CAAE;;CAEF;AACA;AACA,SAAS,4BAA4B,CAAC,MAAM,EAAgB;AAC5D,EAAEC,0CAAoC,CAAC,IAAA,IAAQ;AAC/C,IAAI,MAAM,EAAE,WAAW,EAAE,kBAAmB,GAAE,UAAU,EAAE,CAAA;AAC1D;AACA,IAAI,IAAIC,cAAS,EAAC,KAAM,MAAA,IAAUC,2BAAmB,EAAE,EAAE;AACzD,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAM,EAAA,GAAI,IAAI,CAAA;AAClD;AACA,IAAI,MAAM,KAAM;AAChB,MAAM,UAAU,SAAA,IAAaC,cAAQ,CAAC,GAAG,CAAA;AACzC,UAAU,2BAA2B,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,CAAA;AAC5D,UAAU,6BAA6B;AACvC,YAAYC,kCAAqB,CAAC,WAAW,EAAE,KAAM,IAAG,GAAG,EAAE,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC;AAChG,YAAY,GAAG;AACf,YAAY,IAAI;AAChB,YAAY,MAAM;AAClB,WAAW,CAAA;AACX;AACA,IAAI,KAAK,CAAC,KAAM,GAAE,OAAO,CAAA;AACzB;AACA,IAAIC,iBAAY,CAAC,KAAK,EAAE;AACxB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,SAAS,EAAE;AACjB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,IAAI,EAAE,SAAS;AACvB,OAAO;AACP,KAAK,CAAC,CAAA;AACN,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA,SAAS,yCAAyC,CAAC,MAAM,EAAgB;AACzE,EAAEC,uDAAiD,CAAC,CAAA,IAAK;AACzD,IAAI,MAAM,EAAE,WAAW,EAAE,kBAAmB,GAAE,UAAU,EAAE,CAAA;AAC1D;AACA,IAAI,IAAIL,cAAS,EAAC,KAAM,MAAA,IAAUC,2BAAmB,EAAE,EAAE;AACzD,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,MAAM,KAAM,GAAE,2BAA2B,CAAC,GAAa,CAAA;AAC3D;AACA,IAAI,MAAM,KAAA,GAAQK,iBAAW,CAAC,KAAK,CAAA;AACnC,QAAQ,gCAAgC,CAAC,KAAK,CAAA;AAC9C,QAAQH,kCAAqB,CAAC,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAA;AACpF;AACA,IAAI,KAAK,CAAC,KAAM,GAAE,OAAO,CAAA;AACzB;AACA,IAAIC,iBAAY,CAAC,KAAK,EAAE;AACxB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,SAAS,EAAE;AACjB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,IAAI,EAAE,sBAAsB;AACpC,OAAO;AACP,KAAK,CAAC,CAAA;AACN,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA,SAAS,2BAA2B,CAAC,KAAK,EAAoB;AAC9D,EAAE,IAAIE,iBAAW,CAAC,KAAK,CAAC,EAAE;AAC1B,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA;AACA,EAAE,MAAM,CAAE,GAAE,KAAM,EAAA;AAClB;AACA;AACA,EAAE,IAAI;AACN;AACA;AACA,IAAI,IAAI,QAAS,IAAG,CAAC,EAAE;AACvB,MAAM,OAAO,CAAC,CAAC,MAAM,CAAA;AACrB,KAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,IAAI,QAAA,IAAY,CAAA,IAAK,QAAA,IAAY,CAAC,CAAC,MAAM,EAAE;AACpD,MAAM,OAAO,CAAC,CAAC,MAAM,CAAC,MAAM,CAAA;AAC5B,KAAI;AACJ,GAAE,CAAE,WAAM,EAAC;AACX;AACA,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gCAAgC,CAAC,MAAM,EAAoB;AACpE,EAAE,OAAO;AACT,IAAI,SAAS,EAAE;AACf,MAAM,MAAM,EAAE;AACd,QAAQ;AACR,UAAU,IAAI,EAAE,oBAAoB;AACpC;AACA,UAAU,KAAK,EAAE,CAAC,iDAAiD,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,2BAAA,CAAA,GAAA,EAAA,GAAA,EAAA,IAAA,EAAA,MAAA,EAAA;AACA,EAAA,MAAA,cAAA;AACA,IAAA,0GAAA,CAAA;AACA;AACA;AACA,EAAA,IAAA,OAAA,GAAAC,kBAAA,CAAA,GAAA,CAAA,GAAA,GAAA,CAAA,OAAA,GAAA,GAAA,CAAA;AACA,EAAA,IAAA,IAAA,GAAA,OAAA,CAAA;AACA;AACA,EAAA,MAAA,MAAA,GAAA,OAAA,CAAA,KAAA,CAAA,cAAA,CAAA,CAAA;AACA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,IAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,KAAA,GAAA;AACA,IAAA,SAAA,EAAA;AACA,MAAA,MAAA,EAAA;AACA,QAAA;AACA,UAAA,IAAA,EAAA,IAAA;AACA,UAAA,KAAA,EAAA,OAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,OAAA,6BAAA,CAAA,KAAA,EAAA,GAAA,EAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,6BAAA,CAAA,KAAA,EAAA,GAAA,EAAA,IAAA,EAAA,MAAA,EAAA;AACA;AACA,EAAA,MAAA,CAAA,IAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,SAAA,IAAA,EAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,EAAA,IAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,MAAA,IAAA,EAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,GAAA,IAAA,EAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,IAAA,IAAA,GAAA,CAAA,UAAA,GAAA,GAAA,CAAA,UAAA,IAAA,EAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,KAAA,IAAA,IAAA,CAAA,MAAA,GAAA,IAAA,CAAA,MAAA,IAAA,EAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,KAAA,GAAA,KAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,CAAA,CAAA,GAAA,SAAA,GAAA,MAAA,CAAA;AACA,EAAA,MAAA,MAAA,GAAA,KAAA,CAAA,QAAA,CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,GAAA,SAAA,GAAA,IAAA,CAAA;AACA,EAAA,MAAA,QAAA,GAAAL,cAAA,CAAA,GAAA,CAAA,IAAA,GAAA,CAAA,MAAA,GAAA,CAAA,GAAA,GAAA,GAAAM,qBAAA,EAAA,CAAA;AACA;AACA;AACA,EAAA,IAAA,KAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA,IAAA,KAAA,CAAA,IAAA,CAAA;AACA,MAAA,KAAA;AACA,MAAA,QAAA;AACA,MAAA,QAAA,EAAA,GAAA;AACA,MAAA,MAAA,EAAA,IAAA;AACA,MAAA,MAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,gBAAA,CAAA,IAAA,EAAA;AACA,EAAAC,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,CAAA,yBAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,UAAA,GAAA;AACA,EAAA,MAAA,MAAA,GAAAV,cAAA,EAAA,CAAA;AACA,EAAA,MAAA,OAAA,GAAA,CAAA,MAAA,IAAA,MAAA,CAAA,UAAA,EAAA,KAAA;AACA,IAAA,WAAA,EAAA,MAAA,EAAA;AACA,IAAA,gBAAA,EAAA,KAAA;AACA,GAAA,CAAA;AACA,EAAA,OAAA,OAAA,CAAA;AACA;;;;;"}