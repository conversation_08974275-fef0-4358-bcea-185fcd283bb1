{"version": 3, "file": "httpcontext.js", "sources": ["../../../../src/integrations/httpcontext.ts"], "sourcesContent": ["import { convertIntegrationFnToClass, defineIntegration } from '@sentry/core';\nimport type { Event, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\n\nimport { WINDOW } from '../helpers';\n\nconst INTEGRATION_NAME = 'HttpContext';\n\nconst _httpContextIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    preprocessEvent(event) {\n      // if none of the information we want exists, don't bother\n      if (!WINDOW.navigator && !WINDOW.location && !WINDOW.document) {\n        return;\n      }\n\n      // grab as much info as exists and add it to the event\n      const url = (event.request && event.request.url) || (WINDOW.location && WINDOW.location.href);\n      const { referrer } = WINDOW.document || {};\n      const { userAgent } = WINDOW.navigator || {};\n\n      const headers = {\n        ...(event.request && event.request.headers),\n        ...(referrer && { Referer: referrer }),\n        ...(userAgent && { 'User-Agent': userAgent }),\n      };\n      const request = { ...event.request, ...(url && { url }), headers };\n\n      event.request = request;\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const httpContextIntegration = defineIntegration(_httpContextIntegration);\n\n/**\n * HttpContext integration collects information about HTTP request headers.\n * @deprecated Use `httpContextIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const HttpContext = convertIntegrationFnToClass(INTEGRATION_NAME, httpContextIntegration) as IntegrationClass<\n  Integration & { preprocessEvent: (event: Event) => void }\n>;\n"], "names": ["WINDOW", "defineIntegration", "convertIntegrationFnToClass"], "mappings": ";;;;;AAKA,MAAM,gBAAA,GAAmB,aAAa,CAAA;AACtC;AACA,MAAM,uBAAwB,IAAG,MAAM;AACvC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,eAAe,CAAC,KAAK,EAAE;AAC3B;AACA,MAAM,IAAI,CAACA,cAAM,CAAC,aAAa,CAACA,cAAM,CAAC,YAAY,CAACA,cAAM,CAAC,QAAQ,EAAE;AACrE,QAAQ,OAAM;AACd,OAAM;AACN;AACA;AACA,MAAM,MAAM,GAAI,GAAE,CAAC,KAAK,CAAC,OAAQ,IAAG,KAAK,CAAC,OAAO,CAAC,GAAG,MAAMA,cAAM,CAAC,QAAA,IAAYA,cAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;AACnG,MAAM,MAAM,EAAE,QAAA,EAAW,GAAEA,cAAM,CAAC,QAAA,IAAY,EAAE,CAAA;AAChD,MAAM,MAAM,EAAE,SAAA,EAAY,GAAEA,cAAM,CAAC,SAAA,IAAa,EAAE,CAAA;AAClD;AACA,MAAM,MAAM,UAAU;AACtB,QAAQ,IAAI,KAAK,CAAC,OAAA,IAAW,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AACnD,QAAQ,IAAI,QAAS,IAAG,EAAE,OAAO,EAAE,QAAS,EAAC,CAAC;AAC9C,QAAQ,IAAI,SAAU,IAAG,EAAE,YAAY,EAAE,SAAU,EAAC,CAAC;AACrD,OAAO,CAAA;AACP,MAAM,MAAM,OAAQ,GAAE,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE,IAAI,GAAA,IAAO,EAAE,GAAA,EAAK,CAAC,EAAE,SAAS,CAAA;AACxE;AACA,MAAM,KAAK,CAAC,OAAQ,GAAE,OAAO,CAAA;AAC7B,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,sBAAuB,GAAEC,sBAAiB,CAAC,uBAAuB,EAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAcC,gCAA2B,CAAC,gBAAgB,EAAE,sBAAsB,CAAE;;;;;;;"}